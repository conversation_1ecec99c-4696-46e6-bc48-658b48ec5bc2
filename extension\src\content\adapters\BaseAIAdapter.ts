import { Conversation } from "@/common/types";
import { Platform } from "@/common/types/database_entity";
import {
  PlatformConfig,
  SelectorMatchResult,
  RegexMatchResult,
  SelectorConfig,
} from "@/content/types/PlatformConfigType";
import { FloatingBubbleInject } from "@/content/inject/FloatingBubbleInject";
import { ArchiveButtonInject } from "@/content/inject/ArchiveButtonInject";
import { InputCapture } from "@/content/capture/InputCapture";
import { CommonSelectors } from "@/content/configs/CommonSelectors";

/**
 * 重构后的AI适配器基类
 * 作为 content 模块的核心应用上下文，统一管理组件间交互
 */
export abstract class BaseAIAdapter {
  // 平台配置
  protected config: PlatformConfig;
  protected currentPlatform: Platform = null;
  public mergedSelectors: SelectorConfig = null;

  // 捕捉页面信息模块
  protected inputCapture: InputCapture;

  // 注入UI组件模块
  protected floatingBubbleInject: FloatingBubbleInject;
  protected archiveButtonInject: ArchiveButtonInject;

  constructor(config: PlatformConfig) {
    this.config = config;
    console.log('【EchoSync】BaseAIAdapter created for:', config.name);
    // 不在构造函数中初始化，等待外部调用
  }

  /**
   * 公共初始化方法，等待DOM元素可用后再初始化
   */
  async initialize(): Promise<void> {
    console.log('【EchoSync】Starting BaseAIAdapter initialization for:', this.config.name);

    try {
      // 等待关键DOM元素可用
      await this.waitForKeyElements();

      // 初始化各个模块
      this.initCapture();
      this.initInject();

      console.log('【EchoSync】BaseAIAdapter initialization completed for:', this.config.name);
    } catch (error) {
      console.error('【EchoSync】BaseAIAdapter initialization failed:', error);

      // 即使等待失败，也尝试初始化（用于调试）
      console.log('【EchoSync】Attempting initialization anyway...');
      this.initCapture();
      this.initInject();
    }
  }

  /**
   * 等待关键DOM元素可用
   */
  private async waitForKeyElements(): Promise<void> {
    const selectors = this.mergeSelectors();
    const maxAttempts = 20; // 最多尝试20次
    const delay = 250; // 每次间隔250ms

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`【EchoSync】Waiting for key elements, attempt ${attempt}/${maxAttempts}`);

      // 检查输入框是否存在且可见
      for (const selector of selectors.inputField) {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && this.isElementReady(element)) {
          console.log('【EchoSync】Key elements found, selector:', selector);
          return; // 找到可用元素，退出等待
        }
      }

      // 如果是最后一次尝试，记录警告但继续初始化
      if (attempt === maxAttempts) {
        console.warn('【EchoSync】Key elements not found after maximum attempts, proceeding anyway');
        return;
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  /**
   * 检查元素是否准备就绪（存在且可见）
   */
  private isElementReady(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);

    return rect.width > 0 &&
           rect.height > 0 &&
           style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0';
  }

  /**
   * 初始化选择器
   */
  private initCapture() {
    this.mergedSelectors = this.mergeSelectors();
    this.inputCapture = new InputCapture(this);
  }

  private initInject() {
    // 初始化UI组件注入器
    this.floatingBubbleInject = new FloatingBubbleInject(this);
    this.archiveButtonInject = new ArchiveButtonInject(this);
  }

  /**
   * 获取平台特定的选择器配置
   * 子类必须实现此方法来提供平台特定的选择器
   */
  abstract getSelectors(): SelectorConfig;

  /**
   * 合并通用选择器和平台特定选择器
   * 子类可以重写此方法来自定义合并逻辑
   */
  protected mergeSelectors(): SelectorConfig {
    const platformSelectors = this.getSelectors();
    const common = CommonSelectors;

    // 平台特定选择器优先，然后是通用选择器
    return {
      inputField: [...platformSelectors.inputField, ...common.inputField],
      sendButton: [...platformSelectors.sendButton, ...common.sendButton],
      messageContainer: [
        ...platformSelectors.messageContainer,
        ...common.messageContainer,
      ],
      inputContainer: [
        ...platformSelectors.inputContainer,
        ...common.inputContainer,
      ],
    };
  }

  /**
   * 正则表达式匹配
   */
  protected regexMatch(text: string, pattern: RegExp): RegexMatchResult {
    const match = text.match(pattern);
    return {
      matched: match !== null,
      groups: match || undefined,
      content: match?.[1] || match?.[0] || undefined,
    };
  }

  /**
   * 获取平台配置
   */
  getConfig(): PlatformConfig {
    return this.config;
  }

  /**
   * 设置当前平台信息
   */
  setCurrentPlatform(platform: Platform | null): void {
    this.currentPlatform = platform;
  }
  public getCurrentPlatform(): Platform | null {
    return this.currentPlatform;
  }

  public getInputCapture(): InputCapture {
    return this.inputCapture;
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    // 清理UI组件
    this.floatingBubbleInject.destroy();
    this.archiveButtonInject.destroy();

    // 清理捕捉模块
    this.inputCapture.destroy();

    console.log(`【EchoSync】${this.config.name} adapter destroyed`);
  }
}
