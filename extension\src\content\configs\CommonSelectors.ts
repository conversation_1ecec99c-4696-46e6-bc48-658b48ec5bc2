/**
 * 通用选择器配置
 * 包含跨平台通用的选择器模式
 */

import { SelectorConfig } from "../types/PlatformConfigType";



/**
 * 通用选择器配置类
 * 提供所有平台都可能使用的通用选择器
 */
export const CommonSelectors: SelectorConfig = {
  inputField: [
    // 通用输入框选择器
    'textarea[placeholder*="message" i]',
    'textarea[placeholder*="prompt" i]',
    'textarea[placeholder*="输入" i]',
    'textarea[placeholder*="请输入" i]',
    'div[contenteditable="true"]',
    'input[type="text"]',
    '[role="textbox"]',
    // 常见的输入框类名
    ".input-field",
    ".chat-input",
    ".message-input",
    ".prompt-input",
  ],
  sendButton: [
    // 通用发送按钮选择器
    'button[aria-label*="send" i]',
    'button[aria-label*="发送" i]',
    'button[data-testid*="send" i]',
    'button[type="submit"]',
    "button:has(svg)",
    '[role="button"]:has(svg)',
    // 常见的发送按钮类名
    ".send-button",
    ".submit-button",
    ".send-btn",
    ".chat-send",
  ],
  messageContainer: [
    // 通用消息容器选择器
    "[data-message-author-role]",
    '[data-testid*="message"]',
    ".message",
    ".chat-message",
    ".conversation-item",
    ".message-item",
    '[role="article"]',
    // 常见的消息容器类名
    ".message-container",
    ".chat-container",
    ".conversation-container",
    ".messages",
  ],
  inputContainer: [
    // 通用输入容器选择器
    ".input-container",
    ".chat-input-container",
    ".message-input-container",
    ".prompt-container",
    ".editor-container",
    ".input-wrapper",
    ".chat-editor",
    ".input-area",
    // 表单相关
    "form",
    ".form-container",
    ".input-form",
  ],
};
