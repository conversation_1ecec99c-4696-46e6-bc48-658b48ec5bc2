import { FloatingBubble } from '../components/FloatingBubble'
import { HistoryBubbleInject } from '@/content/inject/HistoryBubbleInject'
import { EchoSyncEventEnum } from '../configs/DOMEnum' // EchoSync 自定义事件枚举
import { FLoatingBubbleDrag } from './FLoatingBubbleDrag'
import { BaseAIAdapter } from '../adapters/BaseAIAdapter'
import { DOMUtils } from '../utils/DOMUtils'

/**
 * 悬浮气泡注入逻辑
 * 处理事件监听、业务逻辑和与其他组件的交互
 */
export class FloatingBubbleInject {
  private component: FloatingBubble
  private historyBubbleInject: HistoryBubbleInject | null = null
  private FLoatingBubbleDrag: FLoatingBubbleDrag | null = null
  private adapter: BaseAIAdapter
  private hoverTimer: number | null = null

  constructor(adapter: BaseAIAdapter) {
    this.adapter = adapter
    this.inject()
    this.setupEventListeners()
    this.initializeHistoryBubble()
  }

  /**
   * 注入到页面
   */
  async inject(): Promise<void> {
    this.component = new FloatingBubble()
    const bubble = this.component.render()
    
    if (bubble) {
      // 添加到DOM
      document.body.appendChild(bubble)
      
      // 初始化拖拽处理
      this.FLoatingBubbleDrag = new FLoatingBubbleDrag(bubble)
      ;(bubble as any).FLoatingBubbleDrag = this.FLoatingBubbleDrag
      
      // 设置气泡事件监听
      this.setupBubbleEventListeners()
      
      console.log('【EchoSync】Floating bubble injected successfully')
    }
  }

  /**
   * 设置全局事件监听
   */
  private setupEventListeners(): void {
    // 监听输入框聚焦事件
    document.addEventListener(EchoSyncEventEnum.INPUT_FOCUSED, (event: any) => {
      console.log('【EchoSync】FloatingBubbleInject received INPUT_FOCUSED event:', event.detail)
      this.handleInputFocused(event.detail)
    })

    // 监听页面变化事件
    document.addEventListener(EchoSyncEventEnum.PAGE_CHANGED, () => {
      this.handlePageChanged()
    })

    // 监听边界回弹事件
    document.addEventListener(EchoSyncEventEnum.SNAP_TO_BOUNDARY, () => {
      this.snapToBoundary()
    })

    // 监听窗口大小变化
    this.setupWindowResizeListener()
  }

  /**
   * 设置气泡DOM事件监听
   */
  private setupBubbleEventListeners(): void {
    const bubble = this.component.getElement()
    if (!bubble) return

    // 鼠标悬停事件
    bubble.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
    bubble.addEventListener('mouseleave', this.handleMouseLeave.bind(this))

    // 点击事件
    bubble.addEventListener('click', this.handleClick.bind(this))

    // 右键点击事件
    bubble.addEventListener('contextmenu', this.handleContextMenu.bind(this))
  }

  /**
   * 处理鼠标悬停进入
   */
  private handleMouseEnter(): void {
    this.component.setHoverEffect(true)
    
    // 延迟显示历史气泡
    this.hoverTimer = window.setTimeout(() => {
      this.showHistoryBubble()
    }, 500)
  }

  /**
   * 处理鼠标悬停离开
   */
  private handleMouseLeave(): void {
    this.component.setHoverEffect(false)
    
    // 清除定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }

    // 延迟隐藏历史气泡
    setTimeout(() => {
      if (!this.isMouseOverHistoryBubble()) {
        this.historyBubbleInject?.hide()
      }
    }, 300)
  }

  /**
   * 处理点击事件
   */
  private handleClick(e: MouseEvent): void {
    // 检查是否是拖拽后的点击
    if (this.FLoatingBubbleDrag && this.FLoatingBubbleDrag.isDragging()) {
      e.preventDefault()
      return
    }

    // 发布气泡点击事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.FLOATING_BUBBLE_CLICKED, {
      detail: { position: { x: e.clientX || 0, y: e.clientY || 0 } }
    }))

    // 触发显示存储的提示词事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.SHOW_STORED_PROMPTS))
  }

  /**
   * 处理右键点击事件
   */
  private handleContextMenu(e: Event): void {
    e.preventDefault()
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.DEBUG_FEATURES))
  }

  /**
   * 处理输入框聚焦
   */
  private handleInputFocused(data: { element: HTMLElement }): void {
    // console.log('【EchoSync】handleInputFocused called with data:', data)
    if (!data || !data.element) {
      console.error('【EchoSync】Invalid data in handleInputFocused:', data)
      return
    }

    // console.log('【EchoSync】Moving bubble to input field, element:', data.element)
    this.component.moveToInputField(data.element)
    console.log('【EchoSync】Floating bubble moved to input field on focus')
  }

  /**
   * 处理页面变化
   */
  private handlePageChanged(): void {
    // 页面变化时重新定位气泡
    setTimeout(() => {
      const inputElement = this.adapter.getInputCapture().getInputElement()
      if (inputElement) {
        this.component.moveToInputField(inputElement)
        console.log('【EchoSync】Floating bubble repositioned after page change')
      }
    }, 200)
  }

  /**
   * 设置窗口大小变化监听
   */
  private setupWindowResizeListener(): void {
    let resizeTimeout: NodeJS.Timeout
    // 监听resize事件
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const inputElement = this.adapter.getInputCapture().getInputElement()
        if (inputElement) {
          this.component.moveToInputField(inputElement)
          console.log('【EchoSync】Floating bubble repositioned after window resize')
        }
      }, 300)
    })
  }

  /**
   * 初始化历史气泡
   */
  private initializeHistoryBubble(): void {
    if (this.historyBubbleInject) return

    this.historyBubbleInject = new HistoryBubbleInject({
      maxItems: 10,
      maxWidth: 320,
      showPlatformIcons: true
    })

    // 监听历史项点击事件
    document.addEventListener(EchoSyncEventEnum.HISTORY_ITEM_CLICK, (event: any) => {
      const { chat } = event.detail
      document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.HANDLE_HISTORY_CLICK, { detail: { chat } }))
    })
  }

  /**
   * 显示历史气泡
   */
  private async showHistoryBubble(): Promise<void> {
    if (!this.historyBubbleInject) return

    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.HISTORY_BUBBLE_OPENED, {
      detail: { trigger: 'hover' }
    }))
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.REQUEST_HISTORY_DATA))
  }

  /**
   * 检查鼠标是否在历史气泡上
   */
  private isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubbleInject) return false
    return this.historyBubbleInject.visible
  }

  /**
   * 移动到输入框附近
   */
  moveToInputField(inputElement: HTMLElement): void {
    this.component.moveToInputField(inputElement)
  }

  /**
   * 移动到默认位置
   */
  moveToDefaultPosition(): void {
    this.component.moveToDefaultPosition()
  }

  /**
   * 边界回弹
   */
  snapToBoundary(): void {
    this.component.snapToBoundary()
  }

  /**
   * 销毁
   */
  destroy(): void {
    // 清理定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }
    //清理监听
    document.removeEventListener(EchoSyncEventEnum.INPUT_FOCUSED, (event: any) => {
      this.handleInputFocused(event.detail)
    })
    document.removeEventListener(EchoSyncEventEnum.PAGE_CHANGED, () => {
      this.handlePageChanged()
    })
    document.removeEventListener(EchoSyncEventEnum.SNAP_TO_BOUNDARY, () => {
      this.snapToBoundary()
    })
    window.removeEventListener('resize', () => {
      this.setupWindowResizeListener()
    })

    // 销毁组件
    this.component.destroy()
    this.historyBubbleInject?.destroy()
    this.FLoatingBubbleDrag?.destroy()

    
    console.log('【EchoSync】FloatingBubbleInject destroyed')
  }
}
