// 数据库DTO类型定义
import type { Platform, ChatHistory, ChatPrompt } from './database_entity'

// 平台相关DTO
export interface CreatePlatformInput {
  name: string
  url: string
  icon?: string
  icon_base64?: string
}

export interface UpdatePlatformInput {
  name?: string
  url?: string
  icon?: string
  icon_base64?: string
  is_delete?: number
}

// 聊天提示词相关DTO
export interface CreateChatPromptInput {
  chat_prompt: string
  chat_uid?: string
  create_time?: number
}

export interface UpdateChatPromptInput {
  chat_prompt?: string
  chat_uid?: string
  is_synced?: number
  is_delete?: number
}

export interface ChatPromptQueryParams {
  chat_uid?: string
  is_synced?: number
  is_delete?: number
  limit?: number
  offset?: number
  page?: number
  order_by?: 'create_time' | 'id'
  order_direction?: 'ASC' | 'DESC'
  search?: string
}

// 聊天历史相关DTO
export interface CreateChatHistoryInput {
  chat_prompt?: string  // 保持向后兼容，但标记为可选
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string[]
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time?: number
}

export interface UpdateChatHistoryInput {
  chat_answer?: string
  chat_uid?: string
  platform_id?: number
  tags?: string[]
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  is_synced?: number
  is_answered?: number
  is_delete?: number
}

export interface ChatHistoryQueryParams {
  platform_id?: number
  chat_uid?: string
  chat_group_name?: string
  is_synced?: number
  is_answered?: number
  is_delete?: number
  limit?: number
  offset?: number
  page?: number
  order_by?: 'create_time' | 'id'
  order_direction?: 'ASC' | 'DESC'
  search?: string
}

// 联合操作DTO
export interface CreateChatWithPromptInput {
  chat_prompt: string
  chat_answer?: string
  platform_id: number
  tags?: string[]
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time?: number
}

export interface CreateChatWithPromptResult {
  chatPrompt: ChatPrompt
  chatHistory: ChatHistory
}

// 查询结果DTO
export interface ChatPromptDetailResult {
  prompt: ChatPrompt
  histories: ChatHistory[]
}

// 数据库操作结果DTO
export interface DatabaseResult<T = any> {
  success: boolean
  data?: T
  error?: string
  affected_rows?: number
  last_insert_id?: number
}

// 分页查询结果DTO
export interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 搜索结果DTO
export interface SearchResult<T> extends PaginatedResult<T> {
  searchTerm: string
}

// 统计DTO
export interface ChatHistoryStats {
  total_count: number
  platform_counts: { [platform_name: string]: number }
  recent_count: number
}

// 数据库事件类型
export type DatabaseEvent =
  | 'connected'
  | 'disconnected'
  | 'error'
  | 'migration_start'
  | 'migration_complete'
  | 'migration_error'

// 数据库事件监听器类型
export type DatabaseEventListener = (event: DatabaseEvent, data?: any) => void

// 事务回调类型
export type TransactionCallback<T> = () => Promise<T>


export interface ChatHistoryWithPlatform extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon: string
  platform_icon_base64?: string
  platform_icon_blob?: Blob
}

// 业务逻辑相关的DTO类型

// 聊天历史与平台信息的DTO
export interface ChatHistoryWithPlatformDto extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon?: string
  platform_icon_base64?: string
}

// 聊天历史与平台和提示词信息的DTO（用于向后兼容）
export interface ChatHistoryWithPlatformAndPromptDto extends ChatHistoryWithPlatformDto {
  chat_prompt: string
}

// 聊天提示词与平台统计的DTO
export interface ChatPromptWithPlatformsDto extends ChatPrompt {
  platforms: Array<{
    platform_id: number
    platform_name: string
    platform_icon?: string
    platform_icon_base64?: string
    count: number
    latest_time: number
  }>
}

// 提示词详情DTO
export interface ChatPromptDetailDto {
  prompt: ChatPrompt
  histories: ChatHistoryWithPlatformDto[]
}