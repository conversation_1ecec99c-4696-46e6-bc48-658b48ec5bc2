import Dexie, { Table } from 'dexie'
import { ChatHistory, Platform, ChatPrompt } from '@/common/types/database_entity'

export class EchoSyncDatabase extends <PERSON><PERSON> {
  // 定义表
  chatHistory!: Table<ChatHistory>
  chatPrompt!: Table<ChatPrompt>
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')

    // 定义数据库结构 - 版本1（根据数据结构设计文档）
    this.version(1).stores({
      chatHistory: '++id, chat_uid, platform_id, chat_answer, chat_group_name, chat_sort, p_uid, create_time, is_synced, is_delete, is_answered, [chat_uid+platform_id]',
      chatPrompt: '++id, &chat_uid, chat_prompt, tags, create_time, is_synced, is_delete',
      platform: '++id, &name, url, icon, icon_base64, is_delete'
    })
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      console.log('【EchoSync】Starting database initialization...')
      await this.open()
      console.log('【EchoSync】Database opened successfully')

      await this.insertDefaultPlatforms()
      console.log('【EchoSync】Default platforms inserted')

      // 验证数据库是否正常工作
      const platformCount = await this.platform.count()
      const chatHistoryCount = await this.chatHistory.count()
      const chatPromptCount = await this.chatPrompt.count()
      console.log('【EchoSync】Database verification - Platforms:', platformCount, 'ChatHistory:', chatHistoryCount, 'ChatPrompt:', chatPromptCount)

      console.log('【EchoSync】Database initialized successfully')
    } catch (error) {
      console.error('【EchoSync】Database initialization failed:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      throw error
    }
  }

  /**
   * 插入默认平台数据
   */
  private async insertDefaultPlatforms(): Promise<void> {
    const defaultPlatforms: Platform[] = [
      {
        id: 1,
        name: 'DeepSeek',
        url: 'https://chat.deepseek.com',
        icon: 'https://chat.deepseek.com/favicon.ico',
        icon_base64: '', // 初始为空，后续可通过程序获取并更新
        is_delete: 0
      },
      {
        id: 2,
        name: 'Kimi',
        url: 'https://kimi.moonshot.cn',
        icon: 'https://kimi.moonshot.cn/favicon.ico',
        icon_base64: '', // 初始为空，后续可通过程序获取并更新
        is_delete: 0
      },
      {
        id: 3,
        name: 'ChatGPT',
        url: 'https://chat.openai.com',
        icon: 'https://chat.openai.com/favicon.ico',
        icon_base64: '', // 初始为空，后续可通过程序获取并更新
        is_delete: 0
      },
      {
        id: 4,
        name: 'Claude',
        url: 'https://claude.ai',
        icon: 'https://claude.ai/favicon.ico',
        icon_base64: '', // 初始为空，后续可通过程序获取并更新
        is_delete: 0
      },
      {
        id: 5,
        name: 'Gemini',
        url: 'https://gemini.google.com',
        icon: 'https://gemini.google.com/favicon.ico',
        icon_base64: '', // 初始为空，后续可通过程序获取并更新
        is_delete: 0
      }
    ]

    // 逐个检查并插入平台数据，确保ID固定
    for (const platform of defaultPlatforms) {
      const existing = await this.platform.get(platform.id)
      if (!existing) {
        await this.platform.put(platform)
        console.log(`Platform ${platform.name} (ID: ${platform.id}) inserted`)
      }
    }
  }

}

// 导出单例实例
export const dexieDatabase = new EchoSyncDatabase()