import { BaseAIAdapter } from './BaseAIAdapter'
import { ClaudeConfig } from '../configs/Consts'
import { SelectorConfig } from '../types/PlatformConfigType'

export class ClaudeAdapter extends BaseAIAdapter {
  constructor() {
    super(ClaudeConfig)
  }

  /**
   * 获取 Claude 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        'div[contenteditable="true"]'
      ],
      sendButton: [
        'button[aria-label*="Send"]',
        'button[type="submit"]'
      ],
      messageContainer: [
        '.font-claude-message',
        '[data-is-streaming]',
        '.message'
      ],
      inputContainer: [
        '.composer-parent',
        '.input-container',
        '.editor-container'
      ]
    }
  }

}
