/**
 * 数据库代理服务
 * 所有IndexedDB操作都通过Service Worker进行，其他模块通过消息机制与background通信
 */

import { MessagingService } from '@/common/service/messagingService'
import { MessageType } from '@/common/types/enums'
import type {
  ChatHistory,
  Platform
} from '@/common/types/database_entity'
import type {
  ChatHistoryWithPlatformDto,
  ChatHistoryWithPlatformAndPromptDto,
  ChatPromptWithPlatformsDto
} from '@/common/types/database_dto'
import type {
  CreateChatHistoryInput,
  UpdateChatHistoryInput,
  DatabaseResult,
  ChatHistoryQueryParams,
  PaginatedResult
} from '@/common/types/database_dto'

/**
 * 聊天历史数据库代理服务
 */
export class ChatHistoryDatabaseProxy {
  private static instance: ChatHistoryDatabaseProxy

  public static getInstance(): ChatHistoryDatabaseProxy {
    if (!ChatHistoryDatabaseProxy.instance) {
      ChatHistoryDatabaseProxy.instance = new ChatHistoryDatabaseProxy()
    }
    return ChatHistoryDatabaseProxy.instance
  }

  /**
   * 安全执行数据库操作，带重试机制
   */
  private async safeExecute<T>(
    operation: () => Promise<DatabaseResult<T>>,
    operationName: string
  ): Promise<DatabaseResult<T>> {
    try {
      const result = await operation()
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      // 如果是扩展上下文失效，提供更友好的错误信息
      if (errorMessage.includes('Extension context invalidated')) {
        console.warn(`【EchoSync】Extension context invalidated during ${operationName}, please refresh the page`)
        return {
          success: false,
          error: 'Extension context invalidated. Please refresh the page.'
        }
      }

      console.error(`【EchoSync】Database proxy ${operationName} error:`, error)
      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * 创建聊天记录
   */
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    return this.safeExecute(
      () => MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_CREATE, input),
      'create chat history'
    )
  }

  /**
   * 获取聊天记录列表
   */
  async getList(params?: ChatHistoryQueryParams): Promise<DatabaseResult<PaginatedResult<ChatHistoryWithPlatformDto>>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_GET_LIST, params)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get chat history list failed'
      }
    }
  }

  /**
   * 获取唯一聊天记录
   */
  async getUniqueChats(params?: { limit?: number; order_direction?: 'ASC' | 'DESC' }): Promise<DatabaseResult<ChatHistoryWithPlatformAndPromptDto[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_GET_UNIQUE, params)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get unique chats failed'
      }
    }
  }

  /**
   * 搜索聊天记录
   */
  async search(searchTerm: string, params?: ChatHistoryQueryParams): Promise<DatabaseResult<ChatHistoryWithPlatformDto[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_SEARCH, {
        searchTerm,
        params
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Search chat history failed'
      }
    }
  }

  /**
   * 获取提示词列表（带平台统计）
   */
  async getPromptsWithPlatforms(params?: { limit?: number; offset?: number; order_direction?: 'ASC' | 'DESC' }): Promise<DatabaseResult<ChatPromptWithPlatformsDto[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS, params)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get prompts with platforms failed'
      }
    }
  }

  /**
   * 更新聊天记录
   */
  async update(id: number, data: UpdateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_UPDATE, {
        id,
        data
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Update chat history failed'
      }
    }
  }

  /**
   * 删除聊天记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_DELETE, { id })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete chat history failed'
      }
    }
  }

  /**
   * 根据聊天UID获取记录
   */
  async getByChatUid(chatUid: string): Promise<DatabaseResult<ChatHistory[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_GET_BY_UID, { chatUid })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get chat history by UID failed'
      }
    }
  }
}

/**
 * 平台数据库代理服务
 */
export class PlatformDatabaseProxy {
  private static instance: PlatformDatabaseProxy

  public static getInstance(): PlatformDatabaseProxy {
    if (!PlatformDatabaseProxy.instance) {
      PlatformDatabaseProxy.instance = new PlatformDatabaseProxy()
    }
    return PlatformDatabaseProxy.instance
  }

  /**
   * 根据名称获取平台
   */
  async getByName(name: string): Promise<DatabaseResult<Platform>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_PLATFORM_GET_BY_NAME, { name })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get platform by name failed'
      }
    }
  }

  /**
   * 根据域名查找平台
   */
  async findByDomain(hostname: string): Promise<DatabaseResult<Platform>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_PLATFORM_GET_BY_DOMAIN, { hostname })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Find platform by domain failed'
      }
    }
  }

  /**
   * 获取所有平台
   */
  async getAll(): Promise<DatabaseResult<Platform[]>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_PLATFORM_GET_LIST, {})
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get platform list failed'
      }
    }
  }

  /**
   * 注意：以下方法需要在background中实现对应的消息处理
   * 目前暂时返回错误，提醒需要实现
   */

  /**
   * 创建平台 - 需要在background中实现
   */
  async create(_input: any): Promise<DatabaseResult<Platform>> {
    return {
      success: false,
      error: 'Platform create not implemented in background service'
    }
  }

  /**
   * 更新平台 - 需要在background中实现
   */
  async update(_id: number, _input: any): Promise<DatabaseResult<Platform>> {
    return {
      success: false,
      error: 'Platform update not implemented in background service'
    }
  }

  /**
   * 删除平台 - 需要在background中实现
   */
  async delete(_id: number): Promise<DatabaseResult<boolean>> {
    return {
      success: false,
      error: 'Platform delete not implemented in background service'
    }
  }

  /**
   * 根据ID获取平台 - 需要在background中实现
   */
  async getById(_id: number): Promise<DatabaseResult<Platform>> {
    return {
      success: false,
      error: 'Platform getById not implemented in background service'
    }
  }

  /**
   * 检查平台favicon是否需要更新
   */
  async checkPlatformFavicon(platformId: number): Promise<DatabaseResult<{ needsUpdate: boolean; reason?: string }>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.CHECK_PLATFORM_FAVICON, { platformId })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Check platform favicon failed'
      }
    }
  }

  /**
   * 更新平台favicon
   */
  async updatePlatformFavicon(platformId: number, faviconBase64: string, faviconUrl?: string): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.UPDATE_PLATFORM_FAVICON, {
        platformId,
        faviconBase64,
        faviconUrl
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Update platform favicon failed'
      }
    }
  }
}

/**
 * 数据库查看代理服务
 */
export class DatabaseViewProxy {
  private static instance: DatabaseViewProxy

  public static getInstance(): DatabaseViewProxy {
    if (!DatabaseViewProxy.instance) {
      DatabaseViewProxy.instance = new DatabaseViewProxy()
    }
    return DatabaseViewProxy.instance
  }

  /**
   * 获取所有表信息
   */
  async getAllTables(): Promise<DatabaseResult<Array<{
    name: string
    displayName: string
    description: string
  }>>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_ALL_TABLES, {})
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get all tables failed'
      }
    }
  }

  /**
   * 获取表数据
   */
  async getTableData(
    tableName: string,
    page: number = 1,
    limit: number = 20
  ): Promise<DatabaseResult<PaginatedResult<any>>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_TABLE_DATA, {
        tableName,
        page,
        limit
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table data failed'
      }
    }
  }

  /**
   * 获取表记录总数
   */
  async getTableCount(tableName: string): Promise<DatabaseResult<number>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_GET_TABLE_COUNT, {
        tableName
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Get table count failed'
      }
    }
  }

  /**
   * 删除表记录
   */
  async deleteRecord(tableName: string, recordId: number): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_DELETE_RECORD, {
        tableName,
        recordId
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete record failed'
      }
    }
  }

  /**
   * 清空表所有数据
   */
  async clearTable(tableName: string): Promise<DatabaseResult<number>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CLEAR_TABLE, {
        tableName
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear table failed'
      }
    }
  }
}

// 导出单例实例
export const chatHistoryDatabaseProxy = ChatHistoryDatabaseProxy.getInstance()
export const platformDatabaseProxy = PlatformDatabaseProxy.getInstance()
export const databaseViewProxy = DatabaseViewProxy.getInstance()
