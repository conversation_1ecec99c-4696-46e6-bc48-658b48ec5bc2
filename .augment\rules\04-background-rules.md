---
type: "agent_requested"
description: "Background模块开发规则"
---
# Background模块开发规则

## 🎯 模块概述

Background模块是Chrome插件的Service Worker，负责消息处理、数据库操作、后台任务管理。采用严格的分层架构，确保数据访问的安全性和一致性。

## 🏗️ 架构设计

### 核心职责
- **消息路由**: 处理来自Content Script、Popup、Options的消息
- **数据库管理**: 统一的数据库访问入口
- **后台任务**: 定时任务、数据同步等后台处理
- **权限管理**: 处理Chrome Extension API权限

### 目录结构
```
background/
├── messageHandler.ts      # 消息路由处理
├── databaseConnection.ts  # 数据库连接管理
├── healthMonitor.ts       # 健康监控
├── taskScheduler.ts       # 任务调度器
└── background.ts          # Service Worker入口
```

## 📡 消息处理规范

### 严格的分层架构
数据库访问必须遵循以下层次：
**Content Script/Popup/Options → MessagingService → Background → Service → DAO → Database**

### 禁止行为
- Content Script直接访问数据库
- UI组件直接调用DAO层
- 跨层级的直接调用
- 绕过消息机制的数据访问

### MessageHandler设计规范
```typescript
// 标准消息处理模式
export const handleMessage = async (
  message: ChromeMessage,
  sender: chrome.runtime.MessageSender,
  sendResponse: (response: any) => void
): Promise<void> => {
  try {
    // 1. 确保数据库连接就绪
    await databaseConnectionManager.ensureConnection()
    
    // 2. 根据消息类型路由到对应服务
    switch (message.type) {
      case MessageType.DB_OPERATION:
        const result = await serviceMethod(message.payload)
        sendResponse(result)
        break
      
      default:
        sendResponse({
          success: false,
          error: `Unknown message type: ${message.type}`
        })
    }
  } catch (error) {
    // 3. 统一错误处理
    console.error('【Background】Message handling error:', error)
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
```

### 消息类型管理
- **位置**: `extension/src/common/types/enums.ts`
- **命名规范**: `DB_ENTITY_OPERATION`格式
- **分类管理**: 按功能模块分组定义
- **版本控制**: 新增消息类型时保持向后兼容

## 🗄️ 数据库操作规则

### Service层规则

#### Service类设计规范
```typescript
export class EntityService {
  private static instance: EntityService

  public static getInstance(): EntityService {
    if (!EntityService.instance) {
      EntityService.instance = new EntityService()
    }
    return EntityService.instance
  }

  async businessMethod(params: InputType): Promise<ServiceResult<OutputType>> {
    try {
      // 1. 数据验证
      const validationResult = this.validateInput(params)
      if (!validationResult.isValid) {
        return {
          success: false,
          error: validationResult.error
        }
      }

      // 2. 业务逻辑处理
      const result = await entityDao.operation(params)

      // 3. 返回标准格式
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('【Service】Operation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Operation failed'
      }
    }
  }
}

// 导出单例实例
export const entityService = EntityService.getInstance()
```

#### Service层职责
- **业务逻辑处理**: 协调多个DAO完成复杂业务
- **数据验证**: 验证输入数据的有效性
- **事务管理**: 处理需要事务的复杂操作
- **错误处理**: 统一的错误处理和日志记录
- **格式转换**: ServiceResult格式的统一返回

### DAO层规则

#### DAO类设计规范
```typescript
export class EntityDao {
  private static instance: EntityDao

  public static getInstance(): EntityDao {
    if (!EntityDao.instance) {
      EntityDao.instance = new EntityDao()
    }
    return EntityDao.instance
  }

  async create(entity: CreateEntityInput): Promise<Entity> {
    await dexieDatabase.initialize()
    
    const entityData = {
      ...entity,
      create_time: Date.now(),
      is_delete: 0
    }
    
    const id = await dexieDatabase.entityTable.add(entityData)
    return { ...entityData, id }
  }

  async findById(id: number): Promise<Entity | null> {
    await dexieDatabase.initialize()
    
    const entity = await dexieDatabase.entityTable.get(id)
    return entity || null
  }

  // 其他CRUD方法...
}

// 导出单例实例
export const entityDao = EntityDao.getInstance()
```

#### DAO层职责
- **纯数据操作**: 只处理数据库的CRUD操作
- **查询优化**: 使用适当的索引和查询策略
- **数据映射**: 在数据库格式和业务对象间转换
- **无业务逻辑**: 不包含任何业务规则
- **单表操作**: 每个DAO只负责一个数据表

### 数据库连接管理

#### 连接管理器设计
```typescript
class DatabaseConnectionManager {
  private isConnected: boolean = false
  private connectionPromise: Promise<void> | null = null

  async ensureConnection(): Promise<void> {
    if (this.isConnected) {
      return
    }

    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = this.initializeDatabase()
    await this.connectionPromise
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await dexieDatabase.initialize()
      this.isConnected = true
      console.log('【Database】Connection established')
    } catch (error) {
      console.error('【Database】Connection failed:', error)
      throw error
    }
  }
}

export const databaseConnectionManager = new DatabaseConnectionManager()
```

#### 连接管理原则
- **延迟连接**: 首次使用时才连接数据库
- **连接复用**: 避免重复创建连接
- **健康检查**: 定期检查连接状态
- **错误恢复**: 连接失败时的重试机制

## 🔧 Service Worker规范

### 生命周期管理
```typescript
// Service Worker安装
chrome.runtime.onInstalled.addListener((details) => {
  console.log('【Background】Extension installed:', details.reason)
  // 初始化逻辑
})

// Service Worker启动
chrome.runtime.onStartup.addListener(() => {
  console.log('【Background】Extension startup')
  // 启动逻辑
})

// 消息监听
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleMessage(message, sender, sendResponse)
  return true // 保持消息通道开放
})
```

### 资源管理
- **内存管理**: 及时清理不需要的数据
- **定时器管理**: 正确清理定时器和间隔器
- **事件监听**: 避免内存泄漏的事件监听
- **异步操作**: 正确处理Promise和async/await

## 🔍 健康监控规范

### 监控指标
- **数据库连接状态**: 连接是否正常
- **消息处理性能**: 消息处理时间统计
- **错误率统计**: 操作成功率监控
- **内存使用**: Service Worker内存占用

### 监控实现
```typescript
class HealthMonitor {
  private metrics = {
    messageCount: 0,
    errorCount: 0,
    avgResponseTime: 0
  }

  recordMessage(startTime: number, success: boolean): void {
    this.metrics.messageCount++
    if (!success) {
      this.metrics.errorCount++
    }
    
    const responseTime = Date.now() - startTime
    this.updateAvgResponseTime(responseTime)
  }

  getHealthStatus(): HealthStatus {
    return {
      isHealthy: this.metrics.errorCount / this.metrics.messageCount < 0.1,
      metrics: this.metrics
    }
  }
}

export const healthMonitor = new HealthMonitor()
```

## 🚀 性能优化规范

### 查询优化
- **索引使用**: 使用适当的索引提高查询性能
- **结果限制**: 限制查询结果数量避免内存溢出
- **分页查询**: 对大量数据使用分页机制
- **查询缓存**: 缓存频繁查询的结果

### 内存优化
- **及时清理**: 及时清理不需要的对象引用
- **避免泄漏**: 正确清理事件监听器和定时器
- **数据结构**: 选择合适的数据结构
- **垃圾回收**: 配合浏览器的垃圾回收机制

## 🛡️ 错误处理规范

### 统一错误格式
```typescript
interface ServiceResult<T> {
  success: boolean
  data?: T
  error?: string
}
```

### 错误处理策略
- **Service层**: 捕获并转换为ServiceResult格式
- **DAO层**: 让错误向上传播，由Service层处理
- **Background**: 统一的try-catch和错误响应
- **日志记录**: 详细记录错误信息和上下文

### 错误分类处理
```typescript
const handleError = (error: unknown): ServiceResult<null> => {
  if (error instanceof ValidationError) {
    return {
      success: false,
      error: `Validation failed: ${error.message}`
    }
  }
  
  if (error instanceof DatabaseError) {
    return {
      success: false,
      error: `Database operation failed: ${error.message}`
    }
  }
  
  return {
    success: false,
    error: 'Unknown error occurred'
  }
}
```

## 📋 开发流程规范

### 新增数据表流程
1. 在dexie.ts中定义表结构和索引
2. 创建对应的Entity类型定义
3. 创建对应的DAO类实现CRUD操作
4. 创建对应的Service方法处理业务逻辑
5. 在MessageHandler中添加消息路由
6. 定义相应的MessageType枚举

### 新增查询方法流程
1. 在DAO中添加查询方法
2. 在Service中添加业务方法
3. 定义新的MessageType
4. 在MessageHandler中添加处理逻辑
5. 更新相关的类型定义

### 数据迁移流程
1. 增加Dexie版本号
2. 定义迁移逻辑和升级脚本
3. 测试数据迁移过程
4. 提供回滚方案
5. 记录迁移日志

## 🔧 调试和监控

### 日志记录规范
```typescript
// 统一日志前缀
console.log('【Background】Operation completed')
console.error('【Database】Connection failed:', error)
console.warn('【Service】Performance warning:', metrics)
```

### 开发工具使用
- **Chrome DevTools**: 查看Service Worker状态
- **IndexedDB查看**: 使用DevTools查看数据库内容
- **网络监控**: 监控消息传递性能
- **内存分析**: 分析内存使用情况

## ✅ 开发检查清单

### 新建Service检查
- [ ] 使用单例模式设计
- [ ] 实现完整的错误处理
- [ ] 返回标准的ServiceResult格式
- [ ] 添加适当的数据验证
- [ ] 记录操作日志
- [ ] 文件大小不超过300行

### 新建DAO检查
- [ ] 使用单例模式设计
- [ ] 只包含纯数据操作
- [ ] 正确使用数据库索引
- [ ] 实现完整的CRUD操作
- [ ] 添加适当的错误处理
- [ ] 单表操作原则

### 消息处理检查
- [ ] 在MessageHandler中正确路由
- [ ] 定义对应的MessageType
- [ ] 实现统一的错误处理
- [ ] 确保数据库连接就绪
- [ ] 返回标准格式的响应
