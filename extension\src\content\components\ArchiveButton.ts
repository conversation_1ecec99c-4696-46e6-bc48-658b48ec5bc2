/**
 * 存档按钮UI组件
 * 专注于样式和布局设计，不包含业务逻辑
 */
export class ArchiveButton {
  private element: HTMLElement | null = null
  private isVisible: boolean = false

  /**
   * 渲染存档按钮
   */
  render(): HTMLElement {
    this.element = document.createElement('div')
    this.element.className = 'echosync-archive-button'
    this.element.innerHTML = '📁'
    this.element.title = '存档当前提示词'

    // 设置按钮样式
    this.element.style.cssText = `
      position: fixed;
      width: 25px;
      height: 25px;
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10001;
      box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      font-size: 12px;
      opacity: 0;
      transform: scale(0.8);
      pointer-events: none;
      user-select: none;
    `

    this.addGlowStyles()
    return this.element
  }

  /**
   * 显示按钮并添加发光效果
   */
  showWithGlow(): void {
    if (!this.element) return

    this.element.style.opacity = '1'
    this.element.style.transform = 'scale(1)'
    this.element.style.pointerEvents = 'auto'
    this.element.style.animation = 'echosync-glow 2s ease-in-out infinite'
    this.element.style.background = 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
    this.element.classList.remove('archived')
    this.element.innerHTML = '📁'
    this.isVisible = true
  }

  /**
   * 显示已存档状态
   */
  showArchivedState(): void {
    if (!this.element) return

    this.element.style.opacity = '1'
    this.element.style.transform = 'scale(1)'
    this.element.style.pointerEvents = 'none'
    this.element.style.animation = 'none'
    this.element.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
    this.element.innerHTML = '✓'
    this.element.classList.add('archived')
    this.isVisible = true
  }

  /**
   * 隐藏按钮
   */
  hide(): void {
    if (!this.element) return

    this.element.style.opacity = '0'
    this.element.style.transform = 'scale(0.8)'
    this.element.style.pointerEvents = 'none'
    this.element.style.animation = 'none'
    this.isVisible = false
  }

  /**
   * 更新按钮位置
   */
  updatePosition(left: number, top: number): void {
    if (!this.element) return

    this.element.style.left = `${left}px`
    this.element.style.top = `${top}px`
  }

  /**
   * 显示存档动画
   */
  showArchiveAnimation(): void {
    if (!this.element) return

    const flyingIcon = this.element.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10001;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this.element.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    setTimeout(() => {
      flyingIcon.style.left = `${window.innerWidth - 100}px`
      flyingIcon.style.top = '20px'
      flyingIcon.style.transform = 'scale(0.3)'
      flyingIcon.style.opacity = '0'
    }, 50)

    setTimeout(() => {
      document.body.removeChild(flyingIcon)
    }, 650)
  }

  /**
   * 添加点击事件监听
   */
  onClick(callback: () => void): void {
    if (this.element) {
      this.element.addEventListener('click', callback)
    }
  }

  /**
   * 获取按钮元素
   */
  getElement(): HTMLElement | null {
    return this.element
  }

  /**
   * 检查是否可见
   */
  isButtonVisible(): boolean {
    return this.isVisible
  }

  /**
   * 添加发光动画样式
   */
  private addGlowStyles(): void {
    if (!document.getElementById('echosync-glow-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-glow-styles'
      style.textContent = `
        @keyframes echosync-glow {
          0%, 100% {
            box-shadow: 0 4px 25px rgba(139, 92, 246, 0.4);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 4px 35px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6);
            transform: scale(1.05);
          }
        }
      `
      document.head.appendChild(style)
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.element) {
      this.element.remove()
      this.element = null
    }
    this.isVisible = false
  }
}