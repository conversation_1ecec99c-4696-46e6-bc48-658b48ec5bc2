---
type: "development_rules"
description: "EchoSync 项目目录结构和文件组织规则"
---

# 项目结构规则

## 核心目录结构

### background/ - 后台服务模块
- **index.ts** - 主入口文件，最多20行，只做协调工作
- **messageHandler.ts** - 消息处理中心，处理所有消息类型
- **eventListeners.ts** - Chrome扩展事件监听管理
- **databaseConnection.ts** - 数据库连接管理
- **keepAlive.ts** - Service Worker保活机制
- **healthMonitor.ts** - 系统健康监控

### common/ - 核心服务与业务逻辑
- **dao/** - 数据访问对象层，每个表一个DAO类
- **database/** - 数据库层，Dexie配置和初始化
- **service/** - 业务服务层，处理复杂业务逻辑
- **types/** - 类型定义，包含实体类型和DTO类型
- **utils.ts** - 通用工具函数

### content/ - 内容脚本模块
- **adapters/** - 平台适配器，每个AI平台一个适配器
- **capture/** - 页面元素捕捉模块
- **components/** - 内容脚本专用UI组件
- **configs/** - 配置文件，选择器和常量定义
- **inject/** - UI注入模块，负责向页面注入UI元素
- **types/** - 内容脚本类型定义
- **utils/** - 内容脚本工具函数
- **ContentScriptManager.ts** - 内容脚本管理器
- **index.ts** - 内容脚本入口

### components/ - 可复用UI组件
- **ui/** - 基础UI组件库
- **PlatformIcon.ts** - 平台图标组件
- **Toast.ts** - 提示消息组件

### popup/ - 弹出窗口UI
- **components/** - 弹窗专用组件
- **pages/** - 弹窗页面组件
- **App.tsx** - 弹窗应用主组件
- **main.tsx** - 弹窗应用入口
- **index.html** - 弹窗HTML模板

### options/ - 选项页面UI
- **OptionsApp.tsx** - 选项页面主组件
- **main.tsx** - 选项页面入口
- **index.html** - 选项页面HTML模板

### stores/ - 状态管理
- **app-store.ts** - 应用全局状态存储

### styles/ - 样式文件
- **globals.css** - 全局样式定义

## 文件命名规则

### 类和组件命名
- **类文件**: PascalCase，如 `ClassName.ts`
- **React组件**: PascalCase，如 `ComponentName.tsx`
- **工具文件**: camelCase，如 `utilityName.ts`
- **配置文件**: camelCase，如 `configName.ts`
- **类型文件**: `types.ts` 或 `TypeName.ts`

### 目录命名
- **模块目录**: camelCase，如 `moduleName/`
- **组件目录**: 小写复数，如 `components/`
- **工具目录**: 小写复数，如 `utils/`

## 文件大小限制

### 严格限制
- **单个文件**: 最大 300 行代码
- **主入口文件**: 最大 20 行（如 background/index.ts）
- **配置文件**: 最大 100 行

### 拆分策略
文件超过限制时必须拆分：
1. **按功能拆分** - 将不同功能分离到独立文件
2. **按层次拆分** - 将复杂逻辑分层处理
3. **按组件拆分** - 将大组件拆分为子组件

## 新文件创建位置规则

### 平台适配器
- **位置**: `extension/src/content/adapters/`
- **命名**: `platformName.ts`
- **要求**: 必须继承 BaseAIAdapter

### UI组件
- **Content Script组件**: `extension/src/content/components/`
- **React组件**: `extension/src/components/`
- **命名**: `ComponentName.ts` 或 `ComponentName.tsx`

### 注入器
- **位置**: `extension/src/content/inject/`
- **命名**: `ComponentNameInject.ts`
- **要求**: 依赖 BaseAIAdapter 实例

### 捕捉器
- **位置**: `extension/src/content/capture/`
- **命名**: `CaptureName.ts`
- **要求**: 遵循 BaseCapture 模式

### 服务类
- **位置**: `extension/src/common/service/`
- **命名**: `ServiceName.ts`
- **要求**: 使用单例模式或静态方法

### DAO类
- **位置**: `extension/src/common/dao/`
- **命名**: `EntityNameDao.ts`
- **要求**: 只负责单一数据表的CRUD操作

## 导入导出规则

### 导入顺序
1. **Node.js 内置模块** - 如 `fs`, `path`
2. **第三方库** - 如 `React`, `clsx`
3. **项目内部模块** - 使用 `@/` 别名
4. **相对路径导入** - 如 `./styles.css`

### 导出规则
- **优先使用命名导出** - 便于重构和树摇
- **默认导出仅用于主要组件** - React组件等
- **避免混合导出** - 一个文件主要使用一种导出方式

## 依赖关系规则

### 模块依赖层次
- **UI层**: popup/options → common/service
- **内容脚本**: content → common/service (仅通过消息)
- **后台服务**: background → common/service → common/dao → common/database

### 禁止的依赖
- content 模块禁止直接导入 common/dao 或 common/database
- UI组件禁止直接导入业务逻辑模块
- 同级模块间禁止循环依赖

## 配置文件组织

### 选择器配置
- **位置**: `extension/src/content/configs/`
- **CommonSelectors.ts** - 通用选择器定义
- **Consts.ts** - 平台配置常量
- **DOMEnum.ts** - DOM相关枚举

### 类型定义
- **位置**: `extension/src/common/types/`
- **database_entity.ts** - 数据库实体类型
- **database_dto.ts** - 数据传输对象类型
- **enums.ts** - 枚举类型定义
- **index.ts** - 类型导出入口

## 代码组织最佳实践

### 单一职责原则
- 每个文件只负责一个明确的功能
- 每个类只处理一个业务领域
- 每个方法只做一件事

### 依赖注入
- 通过构造函数注入依赖
- 避免在类内部直接创建依赖对象
- 使用接口定义依赖契约

### 错误处理
- 使用统一的错误处理模式
- 错误信息要清晰明确
- 使用 console.error 记录详细错误信息

### 注释规范
- 类和方法必须有功能描述注释
- 复杂逻辑必须有行内注释说明
- 使用 JSDoc 格式编写注释
- 注明设计模式的使用（如果适用）
