import { PlatformConfigList } from '@/content/configs/Consts'
import { PlatformConfig } from '../types/PlatformConfigType'

/**
 * 检测当前平台
 */
export function detectCurrentPlatform(): PlatformConfig | null {
  const hostname = window.location.hostname

  for (const config of PlatformConfigList) {
    // 检查主机名匹配
    if (config.patterns.hostname.test(hostname)) {
        console.log('【PlatformDetector】Detected platform:', config.name)
        return config
    }
  }

  console.error('【PlatformDetector】No platform detected')
  return null
}

/**
 * 根据URL检测平台
 */
export function detectPlatformByUrl(url: string): PlatformConfig | null {
  const urlObj = new URL(url)
  const hostname = urlObj.hostname

  for (const config of PlatformConfigList) {
    // 检查主机名匹配
    if (config.patterns.hostname.test(hostname)) {
        console.log('【PlatformDetector】Detected platform by URL:', config.name)
        return config
    }
  }

  console.error('【PlatformDetector】No platform detected by URL')
  return null
}
