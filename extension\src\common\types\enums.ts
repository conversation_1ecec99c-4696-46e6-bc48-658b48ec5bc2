
// 消息类型
export enum MessageType {
    // ... 现有类型
    // 设置相关消息
    GET_SETTINGS = 'GET_SETTINGS',
    // 保存设置
    UPDATE_SETTINGS = 'UPDATE_SETTINGS',

    CAPTURE_PROMPT = 'CAPTURE_PROMPT',
    INJECT_PROMPT = 'INJECT_PROMPT',
    CHECK_PAGE_VALIDITY = 'CHECK_PAGE_VALIDITY',
    GET_PLATFORM_INFO = 'GET_PLATFORM_INFO',
    SHOW_NOTIFICATION = 'SHOW_NOTIFICATION',
  
    // Favicon相关消息
    UPDATE_PLATFORM_FAVICON = 'UPDATE_PLATFORM_FAVICON',
    CHECK_PLATFORM_FAVICON = 'CHECK_PLATFORM_FAVICON',
  
    // 数据库操作消息类型
    DB_CHAT_HISTORY_CREATE = 'DB_CHAT_HISTORY_CREATE',
    DB_CHAT_HISTORY_GET_LIST = 'DB_CHAT_HISTORY_GET_LIST',
    DB_CHAT_HISTORY_GET_UNIQUE = 'DB_CHAT_HISTORY_GET_UNIQUE',
    DB_CHAT_HISTORY_SEARCH = 'DB_CHAT_HISTORY_SEARCH',
    DB_CHAT_HISTORY_UPDATE = 'DB_CHAT_HISTORY_UPDATE',
    DB_CHAT_HISTORY_DELETE = 'DB_CHAT_HISTORY_DELETE',
    DB_CHAT_HISTORY_GET_BY_UID = 'DB_CHAT_HISTORY_GET_BY_UID',
    DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS = 'DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS',
  
    DB_PLATFORM_GET_BY_NAME = 'DB_PLATFORM_GET_BY_NAME',
    DB_PLATFORM_GET_BY_DOMAIN = 'DB_PLATFORM_GET_BY_DOMAIN',
    DB_PLATFORM_GET_LIST = 'DB_PLATFORM_GET_LIST', // 获取所有平台列表

    // 数据库查看相关消息类型
    DB_GET_ALL_TABLES = 'DB_GET_ALL_TABLES',
    DB_GET_TABLE_DATA = 'DB_GET_TABLE_DATA',
    DB_GET_TABLE_COUNT = 'DB_GET_TABLE_COUNT',
    DB_DELETE_RECORD = 'DB_DELETE_RECORD',
    DB_CLEAR_TABLE = 'DB_CLEAR_TABLE'
  }