---
type: "agent_requested"
description: "EchoSync Chrome插件项目总览和通用开发规范"
---
# EchoSync Chrome插件 - 项目总览

## 🎯 项目概述

EchoSync是一个Chrome插件项目，采用经典的4模块架构：Content Script、Popup、Options、Background。项目专注于AI平台的提示词同步和管理功能。

## 🛠️ 技术栈

### Chrome 插件 (extension/)
- **框架**: React 18 + TypeScript 5
- **构建**: Vite 5 + @crxjs/vite-plugin 2
- **样式**: Tailwind CSS 3 + shadcn/ui
- **数据库**: IndexedDB + Dexie
- **状态**: Zustand 4
- **路由**: React Router 6
- **测试**: Jest 29 + React Testing Library

## 📁 项目结构总览

### 核心模块架构
```
extension/src/
├── background/          # Background模块 - Service Worker
├── content/            # Content Script模块 - 页面注入
├── popup/              # Popup模块 - 弹窗界面
├── options/            # Options模块 - 设置页面
├── common/             # 共享服务和业务逻辑
└── components/         # 可复用UI组件
```

### 模块职责划分
- **Background**: Service Worker、消息处理、数据库操作
- **Content Script**: 页面元素捕捉、UI注入、平台适配
- **Popup**: 快速操作界面、状态展示
- **Options**: 配置管理、数据查看、系统设置

## 🎯 核心开发规范

### 必须遵守的要求
1. **文件大小限制**: 单个ts文件不能超过300行，超过必须拆分
2. **简洁设计**: 遵循最简洁设计模式，避免过度设计
3. **继承限制**: 继承不超过2级，更多层次使用组合模式
4. **扩展优先**: 子类优先选择对父类的扩充，而不是完全覆盖
5. **数据库访问**: 所有数据库操作必须通过Message发送给background脚本
6. **测试策略**: 暂时不需要单元测试，专注功能实现

### 文件命名规则
- **类和组件**: PascalCase，如 `ClassName.ts`、`ComponentName.tsx`
- **工具文件**: camelCase，如 `utilityName.ts`
- **配置文件**: camelCase，如 `configName.ts`
- **目录命名**: camelCase或小写复数

### 导入导出规则
1. **Node.js 内置模块** - 如 `fs`, `path`
2. **第三方库** - 如 `React`, `clsx`
3. **项目内部模块** - 使用 `@/` 别名
4. **相对路径导入** - 如 `./styles.css`

## 🎨 UI组件开发规范

### 组件分类
- **可复用组件**: `extension/src/components/` - 跨模块使用
- **Content Script组件**: `extension/src/content/components/` - 原生DOM
- **Popup组件**: `extension/src/popup/components/` - React组件
- **Options组件**: `extension/src/options/components/` - React组件

### 样式管理规范
- **CSS类命名**: 使用 `echosync-` 前缀避免冲突
- **Content Script**: 优先使用内联样式，设置最高z-index
- **React组件**: 使用Tailwind CSS + clsx处理条件样式
- **响应式设计**: 支持移动端、平板、桌面三种断点

### 可访问性要求
- 设置合适的ARIA属性
- 支持键盘导航
- 提供焦点样式反馈
- 确保高对比度模式兼容

## 🏗️ 架构设计原则

### 单一职责原则
- 每个文件只负责一个明确的功能
- 每个类只处理一个业务领域
- 每个方法只做一件事

### 依赖关系规则
- **UI层**: popup/options → common/service(仅通过消息)
- **内容脚本**: content → common/service (仅通过消息)
- **后台服务**: background → common/service → common/dao → common/database

### 禁止的依赖
- content模块禁止直接导入common/dao或common/database
- UI组件禁止直接导入业务逻辑模块
- 同级模块间禁止循环依赖

## 📋 规则文件索引

### 模块专用规则
- **[01-content-script-rules.md](./01-content-script-rules.md)** - Content Script模块开发规则
- **[02-popup-rules.md](./02-popup-rules.md)** - Popup模块开发规则
- **[03-options-rules.md](./03-options-rules.md)** - Options模块开发规则
- **[04-background-rules.md](./04-background-rules.md)** - Background模块开发规则

### 快速查找指南
使用以下关键词快速定位相关规则：
- **"项目总览"** → 查看本文件，获取项目概览
- **"Content Script"** → 查看01文件，了解内容脚本规则
- **"Popup"** → 查看02文件，了解弹窗开发规则
- **"Options"** → 查看03文件，了解设置页面规则
- **"Background"** → 查看04文件，了解后台服务规则

## 🔄 开发流程建议

1. **新功能开发前**: 先查看相关模块规则文件，了解架构约束
2. **组件开发**: 根据模块类型查看对应的开发规则
3. **数据操作**: 严格遵循消息机制，通过Background处理
4. **平台适配**: 遵循中介者模式，通过BaseAIAdapter协调
5. **代码审查**: 对照检查清单验证是否符合规范

## ✅ 通用检查清单

### 新功能开发检查
- [ ] 选择正确的模块位置
- [ ] 遵循文件大小限制（300行）
- [ ] 使用正确的命名规范
- [ ] 实现必要的错误处理
- [ ] 添加适当的注释和文档
- [ ] 遵循模块间依赖规则

### 代码质量检查
- [ ] 单一职责原则
- [ ] 避免循环依赖
- [ ] 使用TypeScript类型定义
- [ ] 实现适当的生命周期管理
- [ ] 及时清理资源和事件监听器
