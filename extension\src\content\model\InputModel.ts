export class InputModel {
  private static instance: InputModel | null = null

  private currChatUid: string = ''

  private constructor() {}

  public static getInstance(): InputModel {
    if (!InputModel.instance) {
      InputModel.instance = new InputModel()
    }
    return InputModel.instance
  }

  /**
   * 生成新的chatUid
   */
  public generateChatUid(): void {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    this.currChatUid = `prompt-${timestamp}-${randomStr}`
  }

  public getCurrChatUid(): string {
    return this.currChatUid
  }
}