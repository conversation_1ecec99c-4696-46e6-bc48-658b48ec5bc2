/**
 * DOM操作工具类
 */
export class DOMUtils {
  /**
   * 等待元素出现
   */
  static waitForElement(selector: string, timeout = 5000): Promise<Element | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector)
        if (element) {
          obs.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  /**
   * 等待页面加载完成
   */
  static waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
        return
      }

      const handleLoad = () => {
        document.removeEventListener('DOMContentLoaded', handleLoad)
        window.removeEventListener('load', handleLoad)
        resolve()
      }

      document.addEventListener('DOMContentLoaded', handleLoad)
      window.addEventListener('load', handleLoad)
    })
  }



  /**
   * 检查元素是否可见
   */
  static isVisibleElement(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)
    
    return rect.width > 0 && 
           rect.height > 0 && 
           style.display !== 'none' && 
           style.visibility !== 'hidden' &&
           style.opacity !== '0'
  }


  /**
   * 查找元素
   * @param selectors 元素选择器数组
   * @returns 找到的第一个容器元素或null
   */
  static findElement(selectors: string[]): HTMLElement | null {
    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement
      if (element) {
        return element
      }
    }
    return null
  }
}
