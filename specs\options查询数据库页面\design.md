# 技术方案设计

## 架构概述

基于现有的EchoSync插件架构，新增数据库查看页面将遵循严格的分层架构设计，通过消息机制与background通信获取数据库数据。

## 技术栈

- **前端框架**: React + TypeScript
- **样式框架**: Tailwind CSS
- **UI组件库**: shadcn/ui
- **状态管理**: zustand
- **数据通信**: databaseProxy.ts + MessagingService

## 系统架构图

```mermaid
graph TD
    A[DatabaseViewPage] --> B[DatabaseStore]
    B --> C[DatabaseProxy]
    C --> D[MessagingService]
    D --> E[Background MessageHandler]
    E --> F[Service Layer]
    F --> G[DAO Layer]
    G --> H[Dexie Database]
    
    A --> I[UI Components]
    I --> J[DataTable]
    I --> K[Pagination]
    I --> L[LoadingSpinner]
    I --> M[ErrorMessage]
```

## 核心模块设计

### 1. 页面组件 (DatabaseViewPage)

**位置**: `extension/src/options/pages/DatabaseViewPage.tsx`

**职责**:
- 页面布局和导航
- 表选择和数据展示协调
- 错误处理和加载状态管理

**主要功能**:
- 表列表展示
- 表切换功能
- 分页控制
- 数据刷新

### 2. 状态管理 (DatabaseStore)

**位置**: `extension/src/options/stores/databaseStore.ts`

**职责**:
- 管理当前选中的表
- 管理表数据和分页状态
- 管理加载和错误状态

**状态结构**:
```typescript
interface DatabaseState {
  currentTable: 'chatHistory' | 'chatPrompt' | 'platform' | null
  tableData: any[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  loading: boolean
  error: string | null
}
```

### 3. 数据访问层扩展

需要在现有的databaseProxy中添加新的方法来支持通用的表数据查询。

**新增方法**:
- `getAllTables()`: 获取所有表名列表
- `getTableData(tableName, page, limit)`: 获取指定表的分页数据
- `getTableCount(tableName)`: 获取表的总记录数

### 4. UI组件设计

#### DataTable组件
**位置**: `extension/src/options/components/DataTable.tsx`

**功能**:
- 表格数据展示
- 字段格式化
- 长文本截断和展开
- 响应式设计

#### Pagination组件
**位置**: `extension/src/options/components/Pagination.tsx`

**功能**:
- 页码导航
- 页面跳转
- 每页条数选择

## 数据流设计

### 数据获取流程

```mermaid
sequenceDiagram
    participant P as DatabaseViewPage
    participant S as DatabaseStore
    participant DP as DatabaseProxy
    participant BG as Background
    participant SV as Service Layer
    participant DB as Database
    
    P->>S: selectTable('chatHistory')
    S->>DP: getTableData('chatHistory', 1, 20)
    DP->>BG: sendMessage(DB_GET_TABLE_DATA)
    BG->>SV: handleGetTableData()
    SV->>DB: query with pagination
    DB-->>SV: return data
    SV-->>BG: return result
    BG-->>DP: response
    DP-->>S: update state
    S-->>P: trigger re-render
```

### 分页处理流程

```mermaid
flowchart TD
    A[用户点击页码] --> B[更新store中的page]
    B --> C[调用getTableData]
    C --> D[发送消息到background]
    D --> E[Service层处理分页查询]
    E --> F[返回分页数据]
    F --> G[更新UI显示]
```

## 消息类型扩展

需要在MessageType枚举中添加新的消息类型：

```typescript
// 新增消息类型
DB_GET_ALL_TABLES = 'DB_GET_ALL_TABLES',
DB_GET_TABLE_DATA = 'DB_GET_TABLE_DATA',
DB_GET_TABLE_COUNT = 'DB_GET_TABLE_COUNT'
```

## Service层扩展

新增DatabaseViewService来处理数据库查看相关的业务逻辑：

**位置**: `extension/src/common/service/DatabaseViewService.ts`

**主要方法**:
- `getAllTables()`: 返回所有表的元信息
- `getTableData(tableName, page, limit)`: 获取表数据
- `getTableCount(tableName)`: 获取表记录总数

## 错误处理策略

1. **网络错误**: 显示重试按钮和错误信息
2. **数据格式错误**: 显示数据解析失败提示
3. **权限错误**: 显示权限不足提示
4. **空数据**: 显示友好的空状态提示

## 性能优化

1. **虚拟滚动**: 对于大量数据使用虚拟滚动
2. **数据缓存**: 缓存已加载的页面数据
3. **懒加载**: 按需加载表数据
4. **防抖处理**: 对搜索和筛选操作进行防抖

## 安全考虑

1. **数据脱敏**: 敏感字段进行脱敏处理
2. **权限控制**: 仅在开发环境或调试模式下可用
3. **数据验证**: 对返回的数据进行格式验证
