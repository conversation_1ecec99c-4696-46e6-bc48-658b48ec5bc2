import { chatPromptDao } from '../dao/ChatPromptDao'
import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { platformDao } from '../dao/PlatformDao'
import { ChatPrompt } from '@/common/types/database_entity'
import {
  ChatPromptWithPlatformsDto,
  ChatPromptDetailDto,
  CreateChatPromptInput,
  UpdateChatPromptInput,
  ChatPromptQueryParams,
  PaginatedResult,
  DatabaseResult
} from '@/common/types/database_dto'

/**
 * 聊天提示词业务服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class ChatPromptService {
  private static instance: ChatPromptService

  public static getInstance(): ChatPromptService {
    if (!ChatPromptService.instance) {
      ChatPromptService.instance = new ChatPromptService()
    }
    return ChatPromptService.instance
  }

  /**
   * 创建聊天提示词记录
   */
  async create(input: CreateChatPromptInput): Promise<DatabaseResult<ChatPrompt>> {
    try {
      const chatPrompt = await chatPromptDao.create({
        chat_prompt: input.chat_prompt,
        chat_uid: input.chat_uid || Date.now().toString(),
        create_time: input.create_time || Date.now(),
        is_delete: 0,
        is_synced: 0,
        
      })

      return {
        success: true,
        data: chatPrompt
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID查找聊天提示词记录
   */
  async findById(id: number): Promise<DatabaseResult<ChatPrompt | null>> {
    try {
      const chatPrompt = await chatPromptDao.findById(id)
      return {
        success: true,
        data: chatPrompt
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据提示词内容查找记录
   */
  async findByPrompt(prompt: string): Promise<DatabaseResult<ChatPrompt | null>> {
    try {
      const chatPrompt = await chatPromptDao.findByPrompt(prompt)
      return {
        success: true,
        data: chatPrompt
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取提示词列表（带平台统计）
   */
  async getPromptsWithPlatforms(params: ChatPromptQueryParams = {}): Promise<DatabaseResult<PaginatedResult<ChatPromptWithPlatformsDto>>> {
    try {
      const {
        limit = 50,
        page = 1,
        order_by = 'create_time',
        order_direction = 'DESC'
      } = params

      const offset = (page - 1) * limit

      // 获取提示词列表
      const prompts = await chatPromptDao.findAll({
        limit,
        offset,
        orderBy: order_by,
        orderDirection: order_direction
      })

      // 为每个提示词获取平台统计
      const dtos: ChatPromptWithPlatformsDto[] = []
      
      for (const prompt of prompts) {
        const dto = await this.convertToDtoWithPlatforms(prompt)
        dtos.push(dto)
      }

      const total = await chatPromptDao.count()

      return {
        success: true,
        data: {
          data: dtos,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取提示词详情（包含所有历史记录）
   */
  async getChatPromptDetail(chatUid: string): Promise<DatabaseResult<ChatPromptDetailDto | null>> {
    try {
      // 获取提示词
      const prompt = await chatPromptDao.findByChatUid(chatUid)
      if (!prompt) {
        return {
          success: true,
          data: null
        }
      }

      // 获取历史记录
      const histories = await chatHistoryDao.findByChatUid(chatUid)

      // 转换为DTO
      const historiesDto = await Promise.all(
        histories.map(async (history) => {
          const platform = await platformDao.findById(history.platform_id)
          return {
            ...history,
            platform_name: platform?.name || 'Unknown',
            platform_url: platform?.url || '',
            platform_icon: platform?.icon || '',
            platform_icon_base64: platform?.icon_base64
          }
        })
      )

      return {
        success: true,
        data: {
          prompt,
          histories: historiesDto
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索提示词
   */
  async search(searchTerm: string, params: {
    limit?: number
    page?: number
  } = {}): Promise<DatabaseResult<PaginatedResult<ChatPromptWithPlatformsDto>>> {
    try {
      const { limit = 50, page = 1 } = params

      // 搜索提示词
      const allPrompts = await chatPromptDao.searchByPrompt(searchTerm, { limit: 1000 })

      // 分页处理
      const total = allPrompts.length
      const offset = (page - 1) * limit
      const paginatedPrompts = allPrompts.slice(offset, offset + limit)

      // 转换为DTO
      const dtos: ChatPromptWithPlatformsDto[] = []
      for (const prompt of paginatedPrompts) {
        const dto = await this.convertToDtoWithPlatforms(prompt)
        dtos.push(dto)
      }

      return {
        success: true,
        data: {
          data: dtos,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新提示词记录
   */
  async update(id: number, input: UpdateChatPromptInput): Promise<DatabaseResult<ChatPrompt>> {
    try {
      const updates: Partial<ChatPrompt> = {}
      
      if (input.chat_prompt !== undefined) updates.chat_prompt = input.chat_prompt
      if (input.chat_uid !== undefined) updates.chat_uid = input.chat_uid
      if (input.is_synced !== undefined) updates.is_synced = input.is_synced
      if (input.is_delete !== undefined) updates.is_delete = input.is_delete

      const updated = await chatPromptDao.update(id, updates)

      return {
        success: true,
        data: updated
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 删除提示词记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const result = await chatPromptDao.softDelete(id)
      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 检查提示词是否存在
   */
  async exists(prompt: string): Promise<DatabaseResult<boolean>> {
    try {
      const exists = await chatPromptDao.exists(prompt)
      return {
        success: true,
        data: exists
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取最新的提示词
   */
  async getLatest(limit: number = 10): Promise<DatabaseResult<ChatPrompt[]>> {
    try {
      const prompts = await chatPromptDao.findLatest(limit)
      return {
        success: true,
        data: prompts
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<DatabaseResult<{
    total: number
    uniquePrompts: number
  }>> {
    try {
      const total = await chatPromptDao.count()
      
      return {
        success: true,
        data: {
          total,
          uniquePrompts: total // 由于提示词已经去重，所以总数就是唯一数
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 将Entity转换为DTO（带平台统计）
   */
  private async convertToDtoWithPlatforms(prompt: ChatPrompt): Promise<ChatPromptWithPlatformsDto> {
    // 获取该提示词对应的历史记录
    const histories = await chatHistoryDao.findByChatUid(prompt.chat_uid)

    // 按平台统计
    const platformStats = new Map<number, {
      platform_id: number
      count: number
      latest_time: number
    }>()

    for (const history of histories) {
      const existing = platformStats.get(history.platform_id)
      if (!existing || history.create_time > existing.latest_time) {
        platformStats.set(history.platform_id, {
          platform_id: history.platform_id,
          count: (existing?.count || 0) + 1,
          latest_time: Math.max(history.create_time, existing?.latest_time || 0)
        })
      } else {
        platformStats.set(history.platform_id, {
          ...existing,
          count: existing.count + 1
        })
      }
    }

    // 获取平台信息
    const platformIds = Array.from(platformStats.keys())
    const platforms = await Promise.all(
      platformIds.map(id => platformDao.findById(id))
    )

    const platformsWithStats = Array.from(platformStats.values()).map(stat => {
      const platform = platforms.find(p => p?.id === stat.platform_id)
      return {
        platform_id: stat.platform_id,
        platform_name: platform?.name || 'Unknown',
        platform_icon: platform?.icon,
        platform_icon_base64: platform?.icon_base64,
        count: stat.count,
        latest_time: stat.latest_time
      }
    })

    return {
      ...prompt,
      platforms: platformsWithStats
    }
  }
}

// 导出单例实例
export const chatPromptService = ChatPromptService.getInstance()
