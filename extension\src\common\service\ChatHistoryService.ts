import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { chatPromptDao } from '../dao/ChatPromptDao'
import { platformDao } from '../dao/PlatformDao'
import { ChatHistory, ChatPrompt, Platform } from '@/common/types/database_entity'
import {
  ChatHistoryWithPlatformDto,
  ChatHistoryWithPlatformAndPromptDto,
  CreateChatHistoryInput,
  UpdateChatHistoryInput,
  ChatHistoryQueryParams,
  PaginatedResult,
  SearchResult,
  DatabaseResult,
  CreateChatWithPromptInput,
  CreateChatWithPromptResult
} from '@/common/types/database_dto'

/**
 * 聊天历史业务服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class ChatHistoryService {
  private static instance: ChatHistoryService

  public static getInstance(): ChatHistoryService {
    if (!ChatHistoryService.instance) {
      ChatHistoryService.instance = new ChatHistoryService()
    }
    return ChatHistoryService.instance
  }

  /**
   * 创建聊天记录（包含提示词和历史记录）
   */
  async createWithPrompt(input: CreateChatWithPromptInput): Promise<DatabaseResult<CreateChatWithPromptResult>> {
    try {
      console.log('【EchoSync】Starting create chat with prompt:', input)

      const now = Date.now()
      let chatPrompt: ChatPrompt
      let chatUid: string

      // 检查是否存在相同提示词
      const existingPrompt = await chatPromptDao.findByPrompt(input.chat_prompt)

      if (existingPrompt) {
        // 复用现有提示词的chat_uid
        chatPrompt = existingPrompt
        chatUid = chatPrompt.chat_uid
        console.log('【EchoSync】Reusing existing prompt with chat_uid:', chatUid)
      } else {
        // 创建新的提示词记录
        chatUid = now.toString()
        chatPrompt = await chatPromptDao.create({
          chat_prompt: input.chat_prompt,
          chat_uid: chatUid,
          create_time: now,
          is_delete: 0,
          is_synced: 0
        })
        console.log('【EchoSync】Created new prompt with chat_uid:', chatUid)
      }

      // 创建聊天历史记录
      const chatHistory = await chatHistoryDao.create({
        chat_answer: input.chat_answer,
        chat_uid: chatUid,
        platform_id: input.platform_id,
        tags: input.tags?.join(','),
        chat_group_name: input.chat_group_name,
        chat_sort: input.chat_sort,
        p_uid: input.p_uid,
        create_time: input.create_time || now,
        is_answered: input.chat_answer ? 1 : 0,
        is_delete:0,
        is_synced:0
      })

      console.log('【EchoSync】Chat with prompt created successfully')

      return {
        success: true,
        data: {
          chatPrompt,
          chatHistory
        }
      }
    } catch (error) {
      console.error('【EchoSync】Failed to create chat with prompt:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 创建聊天历史记录
   */
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistoryWithPlatformDto>> {
    try {
      const chatHistory = await chatHistoryDao.create({
        chat_answer: input.chat_answer,
        chat_uid: input.chat_uid,
        platform_id: input.platform_id,
        tags: input.tags?.join(','),
        chat_group_name: input.chat_group_name,
        chat_sort: input.chat_sort,
        p_uid: input.p_uid,
        create_time: input.create_time || Date.now(),
        is_answered: input.chat_answer ? 1 : 0,
        is_delete:0,
        is_synced:0
      })

      const dto = await this.convertToDto(chatHistory)

      return {
        success: true,
        data: dto
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID查找聊天历史记录
   */
  async findById(id: number): Promise<DatabaseResult<ChatHistoryWithPlatformDto | null>> {
    try {
      const chatHistory = await chatHistoryDao.findById(id)
      if (!chatHistory) {
        return {
          success: true,
          data: null
        }
      }

      const dto = await this.convertToDto(chatHistory)

      return {
        success: true,
        data: dto
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取聊天历史列表（带平台信息）
   */
  async getChatHistoryWithPlatform(params: ChatHistoryQueryParams = {}): Promise<DatabaseResult<PaginatedResult<ChatHistoryWithPlatformDto>>> {
    try {
      const {
        limit = 50,
        page = 1,
        platform_id,
        order_by = 'create_time',
        order_direction = 'DESC'
      } = params

      const offset = (page - 1) * limit

      const chatHistories = await chatHistoryDao.findAll({
        limit,
        offset,
        platformId: platform_id,
        orderBy: order_by,
        orderDirection: order_direction
      })

      const dtos = await Promise.all(
        chatHistories.map(history => this.convertToDto(history))
      )

      const total = await chatHistoryDao.count({ platformId: platform_id })

      return {
        success: true,
        data: {
          data: dtos,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取去重的聊天历史（带提示词信息）
   */
  async getUniqueChats(params: {
    limit?: number
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<DatabaseResult<ChatHistoryWithPlatformAndPromptDto[]>> {
    try {
      const { limit = 20, order_direction = 'DESC' } = params

      // 获取所有提示词，按时间排序
      const prompts = await chatPromptDao.findAll({
        limit: 1000, // 先获取更多数据用于处理
        orderDirection: order_direction
      })

      // 取前N个提示词
      const selectedPrompts = prompts.slice(0, limit)

      // 为每个提示词获取最新的历史记录和平台信息
      const result: ChatHistoryWithPlatformAndPromptDto[] = []

      for (const prompt of selectedPrompts) {
        // 获取该提示词的最新历史记录
        const histories = await chatHistoryDao.findByChatUid(prompt.chat_uid)
        if (histories.length > 0) {
          // 按时间排序，取最新的
          const latestHistory = histories.sort((a, b) => b.create_time - a.create_time)[0]
          
          const dto = await this.convertToDtoWithPrompt(latestHistory, prompt.chat_prompt)
          result.push(dto)
        }
      }

      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索聊天历史
   */
  async searchChatHistory(searchTerm: string, params: {
    limit?: number
    page?: number
    platform_id?: number
  } = {}): Promise<DatabaseResult<SearchResult<ChatHistoryWithPlatformAndPromptDto>>> {
    try {
      const { limit = 50, page = 1, platform_id } = params

      // 搜索提示词
      const matchingPrompts = await chatPromptDao.searchByPrompt(searchTerm, { limit: 1000 })

      // 搜索答案
      const matchingHistories = await chatHistoryDao.searchByAnswer(searchTerm, {
        limit: 1000,
        platformId: platform_id
      })

      // 合并结果
      const resultMap = new Map<string, ChatHistoryWithPlatformAndPromptDto>()

      // 添加匹配提示词的结果
      for (const prompt of matchingPrompts) {
        const histories = await chatHistoryDao.findByChatUid(prompt.chat_uid)
        const filteredHistories = platform_id 
          ? histories.filter(h => h.platform_id === platform_id)
          : histories

        if (filteredHistories.length > 0) {
          const latestHistory = filteredHistories.sort((a, b) => b.create_time - a.create_time)[0]
          const dto = await this.convertToDtoWithPrompt(latestHistory, prompt.chat_prompt)
          resultMap.set(`${prompt.chat_uid}-${latestHistory.platform_id}`, dto)
        }
      }

      // 添加匹配答案的结果
      for (const history of matchingHistories) {
        const prompt = await chatPromptDao.findByChatUid(history.chat_uid)
        if (prompt) {
          const dto = await this.convertToDtoWithPrompt(history, prompt.chat_prompt)
          resultMap.set(`${history.chat_uid}-${history.platform_id}`, dto)
        }
      }

      // 分页处理
      const allResults = Array.from(resultMap.values())
      const total = allResults.length
      const offset = (page - 1) * limit
      const paginatedResults = allResults.slice(offset, offset + limit)

      return {
        success: true,
        data: {
          data: paginatedResults,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          searchTerm
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新聊天历史记录
   */
  async update(id: number, input: UpdateChatHistoryInput): Promise<DatabaseResult<ChatHistoryWithPlatformDto>> {
    try {
      const updates: Partial<ChatHistory> = {}
      
      if (input.chat_answer !== undefined) {
        updates.chat_answer = input.chat_answer
        updates.is_answered = input.chat_answer ? 1 : 0
      }
      if (input.tags !== undefined) updates.tags = input.tags.join(',')
      if (input.chat_group_name !== undefined) updates.chat_group_name = input.chat_group_name
      if (input.chat_sort !== undefined) updates.chat_sort = input.chat_sort
      if (input.p_uid !== undefined) updates.p_uid = input.p_uid
      if (input.is_synced !== undefined) updates.is_synced = input.is_synced
      if (input.is_delete !== undefined) updates.is_delete = input.is_delete

      const updated = await chatHistoryDao.update(id, updates)
      const dto = await this.convertToDto(updated)

      return {
        success: true,
        data: dto
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 删除聊天历史记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const result = await chatHistoryDao.softDelete(id)
      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 将Entity转换为DTO（带平台信息）
   */
  private async convertToDto(chatHistory: ChatHistory): Promise<ChatHistoryWithPlatformDto> {
    const platform = await platformDao.findById(chatHistory.platform_id)
    
    return {
      ...chatHistory,
      platform_name: platform?.name || 'Unknown',
      platform_url: platform?.url || '',
      platform_icon: platform?.icon || '',
      platform_icon_base64: platform?.icon_base64
    }
  }

  /**
   * 将Entity转换为DTO（带平台和提示词信息）
   */
  private async convertToDtoWithPrompt(chatHistory: ChatHistory, chatPrompt: string): Promise<ChatHistoryWithPlatformAndPromptDto> {
    const baseDto = await this.convertToDto(chatHistory)
    
    return {
      ...baseDto,
      chat_prompt: chatPrompt
    }
  }


  /**
   * 根据chat_uid删除所有相关记录
   */
  async deleteByChatUid(chatUid: string): Promise<DatabaseResult<number>> {
    try {
      const count = await chatHistoryDao.softDeleteByChatUid(chatUid)
      return {
        success: true,
        data: count
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<DatabaseResult<{
    total: number
    byPlatform: { [platformName: string]: number }
  }>> {
    try {
      const total = await chatHistoryDao.count()
      const platforms = await platformDao.findAllActive()
      const byPlatform: { [platformName: string]: number } = {}

      for (const platform of platforms) {
        const count = await chatHistoryDao.count({ platformId: platform.id })
        byPlatform[platform.name] = count
      }

      return {
        success: true,
        data: {
          total,
          byPlatform
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
}

// 导出单例实例
export const chatHistoryService = ChatHistoryService.getInstance()
