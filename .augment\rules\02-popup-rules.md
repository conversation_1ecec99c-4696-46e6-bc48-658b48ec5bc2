---
type: "agent_requested"
description: "Popup模块开发规则"
---

# Popup模块开发规则

## 🎯 模块概述

Popup模块是Chrome插件的弹窗界面，提供快速操作和状态展示功能。采用React + TypeScript技术栈，通过消息机制与Background和Content Script通信。

## 🏗️ 架构设计

### 技术栈
- **框架**: React 18 + TypeScript 5
- **样式**: Tailwind CSS 3 + shadcn/ui
- **状态管理**: Zustand 4
- **构建**: Vite 5 + @crxjs/vite-plugin 2

### 目录结构
```
popup/
├── components/         # 弹窗专用组件
├── pages/             # 弹窗页面组件
├── hooks/             # 自定义Hooks
├── stores/            # 状态管理
├── App.tsx            # 弹窗应用主组件
├── main.tsx           # 弹窗应用入口
└── index.html         # 弹窗HTML模板
```

## 📱 UI设计规范

### 尺寸约束
- **宽度**: 固定400px（Chrome插件标准）
- **高度**: 最大600px，内容自适应
- **最小高度**: 200px
- **响应式**: 支持内容动态调整

### 布局原则
- **简洁明了**: 避免复杂的布局和交互
- **快速操作**: 常用功能一键可达
- **状态展示**: 清晰显示当前状态
- **错误提示**: 友好的错误信息展示

### 视觉设计
- **主题色**: 使用项目统一的蓝色主题
- **间距**: 使用Tailwind的标准间距系统
- **字体**: 使用系统默认字体栈
- **图标**: 使用Lucide React图标库

## ⚛️ React组件开发规范

### 组件结构要求
```typescript
interface ComponentProps {
  // 定义清晰的Props接口
}

export const ComponentName: React.FC<ComponentProps> = ({
  // 解构Props
}) => {
  // 组件逻辑
  return (
    // JSX结构
  )
}

export default ComponentName
```

### 组件命名规范
- **文件名**: PascalCase，如`UserProfile.tsx`
- **组件名**: 与文件名保持一致
- **Props接口**: `ComponentNameProps`
- **导出**: 同时提供命名导出和默认导出

### Hooks使用规范
- **useState**: 管理本地状态
- **useEffect**: 处理副作用和生命周期
- **useCallback**: 优化事件处理函数
- **useMemo**: 优化计算密集型操作
- **自定义Hooks**: 抽取可复用的状态逻辑

## 🔄 状态管理规范

### Zustand Store设计
```typescript
interface PopupState {
  // 状态定义
  isLoading: boolean
  error: string | null
  
  // Actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

export const usePopupStore = create<PopupState>((set) => ({
  // 初始状态
  isLoading: false,
  error: null,
  
  // Actions实现
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error })
}))
```

### 状态管理原则
- **单一数据源**: 每个状态只有一个来源
- **不可变更新**: 使用不可变的方式更新状态
- **最小状态**: 只存储必要的状态
- **派生状态**: 通过计算得出的状态不存储

## 📡 消息通信规范

### 与Background通信
```typescript
// 发送消息到Background
const sendToBackground = async (type: MessageType, payload: any) => {
  try {
    const response = await chrome.runtime.sendMessage({
      type,
      payload
    })
    return response
  } catch (error) {
    console.error('Background communication error:', error)
    throw error
  }
}
```

### 与Content Script通信
```typescript
// 发送消息到当前标签页的Content Script
const sendToContentScript = async (type: MessageType, payload: any) => {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
    if (tab.id) {
      const response = await chrome.tabs.sendMessage(tab.id, {
        type,
        payload
      })
      return response
    }
  } catch (error) {
    console.error('Content Script communication error:', error)
    throw error
  }
}
```

### 消息处理规范
- **错误处理**: 所有消息通信都要有错误处理
- **超时处理**: 设置合理的超时时间
- **状态反馈**: 在UI中反映通信状态
- **重试机制**: 对于重要操作提供重试功能

## 🎨 样式开发规范

### Tailwind CSS使用
- **工具类优先**: 优先使用Tailwind工具类
- **自定义样式**: 必要时使用CSS模块或styled-components
- **响应式**: 使用Tailwind的响应式前缀
- **主题定制**: 在tailwind.config.js中定义项目主题

### 组件样式模式
```typescript
const ComponentName: React.FC<Props> = ({ className, ...props }) => {
  return (
    <div 
      className={cn(
        "base-styles",
        "state-styles",
        className
      )}
      {...props}
    >
      {/* 内容 */}
    </div>
  )
}
```

### shadcn/ui组件使用
- **优先使用**: 优先使用shadcn/ui提供的组件
- **主题一致**: 保持与项目主题的一致性
- **自定义扩展**: 必要时基于shadcn/ui组件进行扩展
- **可访问性**: 利用shadcn/ui的可访问性特性

## 🔧 Chrome Extension API使用

### 权限管理
```typescript
// 检查权限
const checkPermissions = async () => {
  const hasPermission = await chrome.permissions.contains({
    permissions: ['activeTab', 'storage']
  })
  return hasPermission
}
```

### 存储API使用
```typescript
// 使用Chrome Storage API
const saveToStorage = async (key: string, value: any) => {
  await chrome.storage.local.set({ [key]: value })
}

const getFromStorage = async (key: string) => {
  const result = await chrome.storage.local.get(key)
  return result[key]
}
```

### 标签页操作
```typescript
// 获取当前活动标签页
const getCurrentTab = async () => {
  const [tab] = await chrome.tabs.query({ 
    active: true, 
    currentWindow: true 
  })
  return tab
}
```

## 🚀 性能优化规范

### 组件优化
- **React.memo**: 对纯组件使用memo包装
- **useCallback**: 缓存事件处理函数
- **useMemo**: 缓存计算结果
- **懒加载**: 对大组件使用React.lazy

### 渲染优化
- **避免内联对象**: 不在render中创建新对象
- **条件渲染**: 使用条件渲染减少DOM节点
- **虚拟化**: 对长列表使用虚拟化
- **防抖节流**: 对频繁操作使用防抖节流

## 🛡️ 错误处理规范

### 错误边界
```typescript
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    console.error('Popup Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />
    }
    return this.props.children
  }
}
```

### 异步错误处理
- **try-catch**: 包装所有异步操作
- **错误状态**: 在状态中管理错误信息
- **用户反馈**: 向用户显示友好的错误信息
- **错误恢复**: 提供错误恢复机制

## 🧪 测试规范

### 组件测试
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import ComponentName from './ComponentName'

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })

  it('should handle click events', () => {
    const handleClick = jest.fn()
    render(<ComponentName onClick={handleClick} />)
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalled()
  })
})
```

### 测试策略
- **单元测试**: 测试单个组件的功能
- **集成测试**: 测试组件间的交互
- **E2E测试**: 测试完整的用户流程
- **快照测试**: 防止意外的UI变更

## ✅ 开发检查清单

### 新建组件检查
- [ ] 使用TypeScript定义Props接口
- [ ] 实现React.FC类型的函数组件
- [ ] 支持className和其他HTML属性透传
- [ ] 使用Tailwind CSS进行样式设计
- [ ] 添加适当的可访问性属性
- [ ] 实现错误处理和加载状态
- [ ] 文件大小不超过300行

### 状态管理检查
- [ ] 使用Zustand创建状态store
- [ ] 定义清晰的状态接口
- [ ] 实现必要的actions
- [ ] 避免状态冗余
- [ ] 处理异步状态更新

### 通信功能检查
- [ ] 正确使用Chrome Extension API
- [ ] 实现错误处理和超时机制
- [ ] 在UI中反映通信状态
- [ ] 测试与Background和Content Script的通信
- [ ] 处理权限和安全问题
