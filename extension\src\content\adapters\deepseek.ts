import { BaseAIAdapter } from './BaseAIAdapter'

import { DeepSeekConfig } from '../configs/Consts'
import { SelectorConfig } from '../types/PlatformConfigType'

export class DeepSeekAdapter extends BaseAIAdapter {
  constructor() {
    super(DeepSeekConfig)
  }

  /**
   * 获取 DeepSeek 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '#chat-input',
        'textarea[placeholder*="DeepSeek"]',
        'textarea[placeholder*="发送消息"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]',
        'textarea[placeholder*="请输入"]'
      ],
      sendButton: [
        '.ds-button--primary',
        '[role="button"].ds-button',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-button',
        '[data-testid*="send"]'
      ],
      messageContainer: [
        '.ds-markdown',
        '._4f9bf79',
        '._9663006',
        '.message-item',
        '.chat-message',
        '.message-container',
        '.conversation'
      ],
      inputContainer: [
        '.chat-input-container',
        '.input-wrapper',
        '.editor-container',
        '.input-area'
      ]
    }
  }

}
