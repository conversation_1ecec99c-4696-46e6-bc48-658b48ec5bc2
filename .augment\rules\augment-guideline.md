---
type: "agent_requested"
description: "规则目录"
---
# EchoSync AI Extension - Augment Guidelines

## 技术栈

### Chrome 插件 (extension/)
- **框架**: React 18 + TypeScript 5
- **构建**: Vite 5 + @crxjs/vite-plugin 2
- **样式**: Tailwind CSS 3 + shadcn/ui
- **数据库**: indexedDB+ dexie
- **状态**: Zustand 4
- **路由**: React Router 6
- **测试**: Jest 29 + React Testing Library

## 核心开发规范

### 必须遵守的要求
1. **文件大小限制**: 单个ts文件不能超过300行，超过必须拆分
2. **简洁设计**: 遵循最简洁设计模式，避免过度设计
3. **继承限制**: 继承不超过2级，更多层次使用组合模式
4. **扩展优先**: 子类优先选择对父类的扩充，而不是完全覆盖
5. **数据库访问**: 所有数据库操作必须通过Message发送给background脚本
6. **测试策略**: 暂时不需要单元测试，专注功能实现

## 开发规则目录

### 📁 项目结构规则
- **文件**: [structure.md](./structure.md)
- **描述**: EchoSync 项目目录结构和文件组织规则
- **内容**: 目录结构、文件命名、模块依赖、代码组织最佳实践

### 🎯 content中介者模式规则
- **文件**: [mediator.md](./mediator.md)
- **描述**: 中介者模式下的选择器管理和组件协调规则
- **内容**: BaseAIAdapter中介者实现、选择器管理、组件间通信、平台适配器开发

### 🎨 UI组件开发规则
- **文件**: [ui-components.md](./ui-components.md)
- **描述**: UI组件开发规则和规范
- **内容**: Content Script UI组件、React UI组件、样式管理、响应式设计、可访问性规范

### 💉 UI注入组件规则
- **文件**: [inject.md](./inject.md)
- **描述**: UI注入组件开发规则
- **内容**: Inject模块开发、FloatingBubbleInject、拖拽功能、样式隔离、错误处理

### 🎣 页面元素捕捉规则
- **文件**: [capture.md](./capture.md)
- **描述**: 页面元素捕捉组件开发规则
- **内容**: Capture组件开发、InputCapture实现、DOMUtils工具、自定义事件规范

### 🗄️ 数据库操作规则
- **文件**: [database.md](./database.md)
- **描述**: 数据库操作流程和规则
- **内容**: 分层架构、消息驱动操作、Background消息处理、错误处理、性能优化

### ⚙️ Options模块开发规则
- **文件**: [options.md](./options.md)
- **描述**: Chrome插件Options模块React开发规则
- **内容**: React Hooks使用、配置数据管理、Chrome Extension API集成、用户体验规范

## 规则使用指南

### 快速查找规则
使用以下关键词快速定位相关规则：
- **"规则目录"** → 查看本文件，获取所有规则概览
- **"项目结构"** → 查看 structure.md，了解文件组织规则
- **"中介者模式"** → 查看 mediator.md，了解组件协调规则
- **"UI组件"** → 查看 ui-components.md，了解组件开发规范
- **"注入组件"** → 查看 inject.md，了解UI注入规则
- **"元素捕捉"** → 查看 capture.md，了解页面捕捉规则
- **"数据库"** → 查看 database.md，了解数据操作规则
- **"Options模块"** → 查看 options.md，了解Options页面开发规则

### 开发流程建议
1. **新功能开发前**: 先查看相关规则文件，了解架构约束
2. **组件开发**: 根据组件类型查看对应的UI规则
3. **数据操作**: 严格遵循数据库操作规则，使用消息机制
4. **平台适配**: 遵循中介者模式，通过BaseAIAdapter协调
5. **代码审查**: 对照检查清单验证是否符合规范

### 规则更新原则
- 规则文件应保持简洁，专注于规范描述
- 避免在规则中包含具体代码实现
- 新增规则需要在本目录文件中更新索引

