---
type: "development_rules"
description: "UI组件开发规则和规范"
---

# UI组件开发规则

- **样式**: Tailwind CSS 3 + shadcn/ui


## 组件分类

### 可复用组件
- **位置**: `extension/src/components/`
- **职责**: 跨模块使用的基础UI组件
- **特点**: 高度抽象，通用性强
- **使用场景**: content,options，popup模块均可使用的图标、按钮、提示，card等基础组件

### Content Script UI组件
- **位置**: `extension/src/content/components/`
- **职责**: 注入到网页中的UI元素
- **特点**: 原生DOM操作，避免框架依赖
- **使用场景**: 浮动小球、存档按钮等页面注入组件

### Popup React UI组件
- **位置**: `extension/src/popup/components/`
- **职责**: Popup页面的UI组件
- **特点**: 基于React框架，可复用
- **使用场景**: 弹窗页面

### Options React UI组件
- **位置**: `extension/src/options/components/`
- **职责**: Options页面的UI组件
- **特点**: 基于React框架，可复用
- **使用场景**: 设置界面

## Content Script UI组件规范

### 基础组件结构要求
- **状态管理**: 维护 element 和 isRendered 状态
- **渲染机制**: 实现 render() 方法返回 DOM 元素
- **样式应用**: 通过 applyStyles() 设置内联样式
- **模板系统**: 通过 getTemplate() 返回 HTML 模板
- **事件管理**: 记录所有事件监听器以便清理
- **生命周期**: 提供 destroy() 方法清理资源

### 必须实现的方法
- **render()**: 渲染组件并返回DOM元素
- **createElement()**: 创建DOM元素
- **getClassName()**: 返回CSS类名
- **getTemplate()**: 返回HTML模板
- **applyStyles()**: 应用基础样式
- **applyComponentStyles()**: 应用组件特定样式
- **setupEventListeners()**: 设置事件监听器
- **destroy()**: 销毁组件和清理资源

### 事件处理规范
- 使用 addEventListener() 方法记录事件监听器
- 在 destroy() 中清理所有事件监听器
- 通过 CustomEvent 与其他组件通信
- 使用 `echosync:` 前缀命名自定义事件

### FloatingBubble 组件特殊要求
- **位置管理**: 维护 position 状态，支持位置设置
- **悬停效果**: 实现 setHoverEffect() 方法
- **边界检测**: 实现边界吸附功能
- **拖拽支持**: 配合 FLoatingBubbleDrag 实现拖拽
- **输入框定位**: 提供 moveToInputField() 方法

## Popup与Options的React UI组件规范

### 基础结构要求
- 使用 TypeScript 定义 Props 接口
- 使用 React.FC 类型定义组件
- 支持 className 和 children 属性
- 使用 clsx 库处理条件样式

### 组件命名规范
- 使用 PascalCase 命名组件
- 文件名与组件名保持一致
- 导出默认组件和命名组件

### Props 设计原则
- 定义清晰的 Props 接口
- 提供合理的默认值
- 支持常用的HTML属性透传
- 使用可选属性减少必需参数

### 状态管理规范
- 优先使用 useState 管理本地状态
- 使用 useEffect 处理副作用
- 避免过度使用 useCallback 和 useMemo
- 状态提升到合适的父组件

## 样式管理规范

### CSS类命名规范
- 使用统一的 `echosync-` 前缀避免样式冲突
- 组件类: `echosync-floating-bubble`, `echosync-history-bubble`
- 状态类: `echosync-bubble--hover`, `echosync-bubble--active`
- 尺寸类: `echosync-icon--sm`, `echosync-icon--md`, `echosync-icon--lg`

### 内联样式使用规则
- Content Script 组件优先使用内联样式
- 设置最高 z-index (2147483647) 确保显示层级
- 使用 Object.assign 批量设置样式
- 包含位置、交互、布局、视觉等基础样式

### CSS-in-JS 使用规范
- React组件使用 Tailwind CSS 类
- 使用 clsx 库处理条件样式
- 支持响应式设计类名
- 提供状态相关的样式变化

## 响应式设计规范

### 视口适配策略
- 检测视口大小并调整组件
- 移动端(<768px)、平板(768-1024px)、桌面(>1024px)
- 实现 adjustForViewport() 方法
- 动态应用不同设备的样式

### 断点管理
- 使用标准的响应式断点
- 优先考虑移动端体验
- 确保组件在所有设备上可用
- 测试不同分辨率下的显示效果

## 可访问性规范

### ARIA属性要求
- 设置合适的 role 属性
- 提供 aria-label 描述
- 支持 tabindex 键盘导航
- 添加必要的 aria-* 属性

### 键盘导航支持
- 监听 keydown 事件
- 支持 Enter 和 Space 键激活
- 提供焦点样式反馈
- 实现逻辑焦点顺序

### 视觉反馈规范
- 提供清晰的悬停状态
- 显示焦点指示器
- 支持高对比度模式
- 确保颜色不是唯一的信息载体

## 性能优化规范

### 虚拟化长列表
- 对于超过50项的列表使用虚拟化
- 实现 VirtualizedList 组件
- 只渲染可见区域的项目
- 动态计算滚动位置和项目高度

### 防抖和节流
- 对频繁触发的事件使用防抖
- 对滚动事件使用节流
- 实现通用的 debounce 和 throttle 工具
- 合理设置延迟时间

### 内存管理
- 及时清理事件监听器
- 避免内存泄漏
- 清理定时器和异步操作
- 使用 WeakMap 存储临时数据

## 开发检查清单

### 新建UI组件时检查
- [ ] 选择正确的组件类型（Content Script 或 React）
- [ ] 继承基础组件类或遵循组件模式
- [ ] 实现必要的生命周期方法
- [ ] 使用统一的CSS类命名规范
- [ ] 添加适当的ARIA属性
- [ ] 支持键盘导航
- [ ] 实现响应式设计
- [ ] 添加错误处理
- [ ] 编写单元测试
- [ ] 文件大小不超过 300 行

### 样式开发检查
- [ ] 使用 `echosync-` 前缀
- [ ] 设置合适的 z-index
- [ ] 支持不同设备尺寸
- [ ] 提供状态反馈
- [ ] 确保可访问性
- [ ] 避免样式冲突

### 性能优化检查
- [ ] 避免不必要的重渲染
- [ ] 使用防抖/节流处理频繁事件
- [ ] 及时清理资源
- [ ] 优化长列表渲染
- [ ] 监控内存使用
- [ ] 测试不同设备性能

