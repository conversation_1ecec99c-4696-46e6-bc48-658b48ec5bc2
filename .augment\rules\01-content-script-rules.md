---
type: "agent_requested"
description: "Content Script模块开发规则"
---

# Content Script模块开发规则

## 🎯 模块概述

Content Script模块负责在网页中注入UI元素、捕捉页面元素、适配不同AI平台。采用中介者模式，通过BaseAIAdapter协调各子模块间的通信。

## 🏗️ 架构设计

### 核心组件
- **BaseAIAdapter**: 中介者，协调所有子模块
- **Capture模块**: 页面元素捕捉（InputCapture等）
- **Inject模块**: UI注入（FloatingBubbleInject等）
- **Components**: UI组件（FloatingBubble等）
- **平台适配器**: 各AI平台的具体适配器

### 目录结构
```
content/
├── adapters/           # 平台适配器
├── capture/           # 页面元素捕捉
├── inject/            # UI注入模块
├── components/        # UI组件
├── configs/           # 配置文件
├── utils/             # 工具函数
└── ContentScriptManager.ts
```

## 🎭 中介者模式规则

### BaseAIAdapter作为唯一中介者
- 所有子模块必须通过BaseAIAdapter进行通信
- 禁止子模块间的直接交互
- BaseAIAdapter负责协调所有子模块的行为

### 选择器统一管理
- 平台特定选择器由具体Adapter提供
- 通用选择器在CommonSelectors中定义
- 通过mergeSelectors方法统一合并
- 平台选择器优先级高于通用选择器

### 基础结构要求
```typescript
abstract class BaseAIAdapter {
  abstract getSelectors(): PlatformConfigType
  protected mergeSelectors(): void
  protected initCapture(): void
  protected initInject(): void
  public destroy(): void
}
```

## 🎣 页面元素捕捉规则

### 核心设计原则
- **职责分离**: Capture模块只观察页面元素，不修改DOM
- **中介者模式**: 通过BaseAIAdapter进行通信
- **事件驱动**: 使用自定义事件进行组件间通信

### Capture组件开发规范
- **位置**: `extension/src/content/capture/`
- **命名**: `ComponentNameCapture.ts`
- **继承**: 继承BaseCapture或遵循相同模式
- **依赖**: 通过构造函数接收BaseAIAdapter实例

### 必须实现的方法
```typescript
class BaseCapture {
  captureElement(): Element | null
  initEventListener(): void
  destroy(): void
}
```

### 选择器使用规则
- 必须使用DOMUtils.findElement方法查找元素
- 使用adapter.mergedSelectors获取合并后的选择器
- 选择器优先级：平台特定 > 通用选择器

### 事件监听规范
- 使用原生addEventListener进行事件监听
- 记录所有事件监听器以便清理
- 通过自定义事件与其他组件通信
- 事件命名使用'echosync:'前缀，定义在DOMEnum.ts中

## 💉 UI注入组件规则

### 核心设计原则
- **职责分离**: Inject模块负责UI的创建、注入、销毁
- **非侵入性**: 不影响原页面的正常功能
- **样式隔离**: 使用最高z-index确保显示层级

### Inject组件开发规范
- **位置**: `extension/src/content/inject/`
- **命名**: `ComponentNameInject.ts`
- **依赖**: 通过构造函数接收BaseAIAdapter实例
- **组件**: 创建对应的Component实例

### 必须实现的方法
```typescript
class BaseInject {
  inject(): void
  findContainer(): Element
  setupEventListeners(): void
  destroy(): void
}
```

### 注入流程规范
1. 检查是否已注入，避免重复注入
2. 调用组件的render()方法获取DOM元素
3. 查找合适的父容器
4. 将元素添加到父容器
5. 设置事件监听器
6. 标记注入状态

## 🎨 UI组件开发规范

### Content Script UI组件特点
- **原生DOM操作**: 避免框架依赖
- **样式隔离**: 使用内联样式避免冲突
- **高z-index**: 确保显示在最上层

### 基础组件结构要求
```typescript
class BaseComponent {
  element: HTMLElement | null
  isRendered: boolean
  
  render(): HTMLElement
  createElement(): HTMLElement
  getTemplate(): string
  applyStyles(): void
  setupEventListeners(): void
  destroy(): void
}
```

### 样式管理规范
- **CSS类命名**: 使用`echosync-`前缀避免冲突
- **内联样式**: 优先使用内联样式
- **z-index**: 设置最高值(2147483647)
- **响应式**: 检测视口尺寸进行适配

## 🔧 平台适配器规则

### 继承要求
- 必须继承BaseAIAdapter
- 实现getSelectors()抽象方法
- 可重写事件处理方法添加平台特有逻辑

### 选择器定义规则
```typescript
getSelectors(): PlatformConfigType {
  return {
    inputField: ['platform-specific-selector'],
    sendButton: ['platform-specific-selector'],
    messageContainer: ['platform-specific-selector']
  }
}
```

### 文件组织
- **位置**: `extension/src/content/adapters/platformName.ts`
- **命名**: `PlatformNameAdapter`类
- **导出**: 默认导出适配器类
- **大小**: 单文件不超过300行

## 🔄 组件间通信规则

### 事件驱动通信
- 子模块通过CustomEvent派发事件
- 事件命名使用'echosync:'前缀
- 事件数据通过detail属性传递
- 事件枚举定义在DOMEnum.ts中

### 禁止的通信方式
- 子模块间直接引用
- 子模块间直接方法调用
- 全局变量共享状态

### 正确的通信方式
```typescript
// 派发事件
document.dispatchEvent(new CustomEvent('echosync:prompt-send', {
  detail: { prompt: 'user input' }
}))

// 监听事件
document.addEventListener('echosync:prompt-send', (event) => {
  const { prompt } = event.detail
  // 处理逻辑
})
```

## 🛠️ 工具和配置

### DOMUtils工具规范
- **findElement()**: 按优先级查找元素
- **isElementVisible()**: 检查元素可见性
- **waitForElement()**: 等待元素出现

### 选择器配置管理
- **CommonSelectors.ts**: 通用选择器定义
- **Consts.ts**: 平台配置常量
- **DOMEnum.ts**: DOM相关枚举

## ✅ 开发检查清单

### 新增平台适配器检查
- [ ] 继承BaseAIAdapter
- [ ] 实现getSelectors()抽象方法
- [ ] 提供平台特定的选择器配置
- [ ] 在ContentScriptManager中注册
- [ ] 测试选择器的有效性
- [ ] 文件大小不超过300行

### 新建Capture组件检查
- [ ] 继承BaseCapture或遵循相同模式
- [ ] 通过构造函数接收BaseAIAdapter实例
- [ ] 实现captureElement()方法
- [ ] 实现initEventListener()方法
- [ ] 实现destroy()方法
- [ ] 使用DOMUtils.findElement查找元素
- [ ] 通过自定义事件与其他组件通信

### 新建Inject组件检查
- [ ] 通过构造函数接收BaseAIAdapter实例
- [ ] 实现inject()方法
- [ ] 实现destroy()方法
- [ ] 创建对应的Component类
- [ ] 设置适当的事件监听器
- [ ] 处理注入失败的情况
- [ ] 使用统一的CSS类命名
