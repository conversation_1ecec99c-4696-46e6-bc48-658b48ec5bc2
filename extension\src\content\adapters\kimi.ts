import { BaseAIAdapter } from './BaseAIAdapter'
import { KimiConfig } from '../configs/Consts'
import { SelectorConfig } from '../types/PlatformConfigType'

export class KimiAdapter extends BaseAIAdapter {
  constructor() {
    console.log('【EchoSync】KimiAdapter constructor called')
    super(KimiConfig)
    console.log('【EchoSync】KimiAdapter initialized with config:', KimiConfig)

  }

  /**
   * 获取 Kimi 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '[data-lexical-editor="true"]',
        '.chat-input-editor[contenteditable="true"]',
        '.chat-input-editor',
        'textarea[placeholder*="请输入"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]'
      ],
      sendButton: [
        '.send-button-container:not(.disabled) .send-button',
        '.send-button:not(.disabled)',
        '.chat-editor-action .send-button-container:not(.disabled) .send-button',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-btn',
        '[data-testid*="send"]'
      ],
      messageContainer: [
        '.chat-content-item',
        '.segment',
        '.message-item',
        '.conversation-item',
        '.message-wrapper',
        '.chat-message'
      ],
      inputContainer: [
        '.chat-input-container',
        '.chat-editor',
        '.input-wrapper',
        '.editor-container'
      ]
    }
  }

}
