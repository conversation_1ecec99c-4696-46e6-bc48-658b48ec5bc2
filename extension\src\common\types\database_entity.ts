// 数据库实体类型定义

// 基础实体类型
export interface DatabaseRow {
  id?: number
  created_at?: number
  updated_at?: number
}

// 平台实体
export interface Platform extends DatabaseRow {
  id?: number
  name: string // 唯一索引
  url: string
  icon?: string
  icon_base64?: string // 平台图标的base64编码，用于展示
  is_delete?: number // 是否删除，0否1是，默认0
}

// 聊天提示词实体
export interface ChatPrompt extends DatabaseRow {
  id?: number
  chat_uid: string // 唯一索引，聊天的唯一id，暂时使用秒级别时间戳
  chat_prompt: string // 聊天页面输入框的提示词
  tags?: string[] // 标签数组，暂时为空
  create_time: number // 创建时间 取时间戳
  is_synced: number // 是否已同步过，0否1是，默认0
  is_delete: number // 是否删除，0否1是，默认0
}

// 聊天历史实体
export interface ChatHistory extends DatabaseRow {
  id?: number
  chat_uid: string // 聊天的唯一id，暂时使用秒级别时间戳
  platform_id: number // 平台，比如是deepseek还是chatgpt等等
  chat_answer?: string // 聊天页面输入提示词对应的回答
  chat_group_name?: string // 聊天组名称，一般位于对话的顶部
  chat_sort?: number // 有的平台，比如像deepseek可以获得本次问题在本对话的序号
  p_uid?: string // 从当前平台获得的，本次文词在当前聊天平台的唯一id，有的是session_id+序号
  create_time: number // 创建时间 取时间戳
  is_synced: number // 是否已同步过，0否1是，默认0
  is_delete: number // 是否删除，0否1是，默认0
  is_answered: number // 是否已回答，0否1是
}

